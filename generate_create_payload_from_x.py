import csv
import json
import os
from datetime import datetime
from typing import Dict, List, Tuple, Set
import hashlib

INPUT_CSV = "/Users/<USER>/program/lesson/x.csv"
OUT_NDJSON = "/Users/<USER>/program/lesson/create_students_payload.from_x.ndjson"
OUT_REPORT = "/Users/<USER>/program/lesson/create_students_payload.from_x.report.md"

# 课程映射
COURSE_NAME_TO_ID: Dict[str, int] = {
    "杨教练一对一": 1,
    "武教练一对一": 2,
    "张鲁风一对一": 3,
    "大课": 4,
    "肖教练一对一": 5,
    "肖教练一对二": 6,
    "上官一对一": 7,
    "苗教练一对一": 8,
    "张教练一对一": 9,
    "杨教练一对二": 10,
    "武教练一对二": 11,
}

# 课程名称 -> 课程类型名 映射
COURSE_NAME_TO_TYPE: Dict[str, str] = {
    "杨教练一对一": "一对一",
    "武教练一对一": "一对一",
    "张鲁风一对一": "一对一",
    "大课": "大课",
    "肖教练一对一": "一对一",
    "肖教练一对二": "一对二",
    "上官一对一": "一对一",
    "苗教练一对一": "一对一",
    "张教练一对一": "一对一",
    "杨教练一对二": "一对二",
    "武教练一对二": "一对二",
}

# 来源映射（默认：上门咨询 29）
DEFAULT_SOURCE_ID = 29
# 默认年龄与校区
DEFAULT_AGE = 10
DEFAULT_CAMPUS_ID = 1

# 性别推断（启发式，不保证准确）
FEMALE_MARKERS = set(list("芳菲萌萱颖娇娜雯清倩婷雪霞妍梦欣甜婉晴晗露玲诗怡悦娟璐璇娅茜宁薇伊彤瑶霏蔓妤珂姝璐妃萌淇璟璐函滢潆茹涵曦柯霖筱"))
MALE_MARKERS = set(list("宇浩博轩霖涛勇阳伟斌锋磊强刚超晨昊睿轩皓宁威凯骁鹏健辉鑫琦麒骐程勋鸣锐坤骏伦瀚宸祺诚泽政岳朗驰峻坤瀚然靖卿"))


def detect_header(path: str) -> Tuple[List[str], List[List[str]], int]:
    rows: List[List[str]] = []
    with open(path, "r", encoding="utf-8-sig", newline="") as f:
        r = csv.reader(f)
        for row in r:
            rows.append(row)
    header_idx = 0
    for i, row in enumerate(rows[:5]):
        if any(cell for cell in row):
            if any("姓名" in cell for cell in row):
                header_idx = i
                break
    header = rows[header_idx]
    data = rows[header_idx + 1 :]
    return header, data, header_idx


def find_col_idx(header: List[str], name: str) -> int:
    for i, h in enumerate(header):
        if h.strip() == name:
            return i
    # fallback: contains
    for i, h in enumerate(header):
        if name in h:
            return i
    return -1


def parse_date(value: str) -> Tuple[str, bool]:
    v = (value or "").strip()
    if not v:
        return "", False
    # support formats like 2023/10/18 or 2023-10-18
    for fmt in ("%Y/%m/%d", "%Y-%m-%d", "%Y/%m/%d %H:%M:%S", "%Y-%m-%d %H:%M:%S"):
        try:
            dt = datetime.strptime(v, fmt)
            return dt.strftime("%Y-%m-%d"), True
        except ValueError:
            continue
    return v, False


def generate_virtual_phone(name: str) -> str:
    # deterministic 11-digit mobile-like number
    digest = hashlib.md5((name or "").encode("utf-8")).hexdigest()
    prefixes = [
        "139", "138", "137", "136", "135", "159", "158", "157",
        "178", "188", "187", "186", "185", "184", "183", "182",
        "181", "180", "173", "172", "171", "170", "199"
    ]
    prefix = prefixes[int(digest[8:10], 16) % len(prefixes)]
    suffix_num = int(digest[:8], 16) % 10_000_000_00
    suffix = f"{suffix_num:08d}"
    return f"{prefix}{suffix}"


def enforce_phone_11(value: str, name_for_virtual: str) -> Tuple[str, List[str]]:
    issues: List[str] = []
    digits = ''.join(ch for ch in (value or "") if ch.isdigit())
    if len(digits) >= 11:
        if len(digits) > 11:
            issues.append("phone_truncated")
        return digits[:11], issues
    issues.append("phone_generated")
    return generate_virtual_phone(name_for_virtual), issues


def infer_gender(name: str) -> Tuple[str, str]:
    if not name:
        return "MALE", "unknown"
    separators = [" ", ",", "、", "/", "-", "_", "·"]
    if any(sep in name for sep in separators) or (len(name) >= 6 and any(c in name for c in ["和", "与"])):
        return "MALE", "unknown"
    female_hits = sum(1 for c in name if c in FEMALE_MARKERS)
    male_hits = sum(1 for c in name if c in MALE_MARKERS)
    if female_hits > male_hits and female_hits >= 1:
        return "FEMALE", "low" if female_hits == 1 else "high"
    if male_hits > female_hits and male_hits >= 1:
        return "MALE", "low" if male_hits == 1 else "high"
    if name and name[-1] in FEMALE_MARKERS:
        return "FEMALE", "low"
    return "MALE", "unknown"


def map_course_id(course_name: str) -> Tuple[int, bool]:
    name = (course_name or "").strip()
    if not name:
        return 0, False
    if name in COURSE_NAME_TO_ID:
        return COURSE_NAME_TO_ID[name], True
    norm = name.replace(" ", "")
    for k, v in COURSE_NAME_TO_ID.items():
        if k.replace(" ", "") == norm:
            return v, True
    return 0, False


def map_course_type(course_name: str) -> str:
    return COURSE_NAME_TO_TYPE.get(course_name.strip(), "")


def derive_coach_name(course_name: str) -> str:
    name = (course_name or "").strip()
    if not name or name == "大课":
        return ""
    # remove suffix like 一对一 / 一对二
    for suf in ("一对一", "一对二"):
        if name.endswith(suf):
            return name[: -len(suf)]
    return name


def main() -> None:
    header, data, header_idx = detect_header(INPUT_CSV)
    idx_name = find_col_idx(header, "姓名")
    idx_course = find_col_idx(header, "课程")
    idx_date = find_col_idx(header, "日期")
    idx_phone = find_col_idx(header, "电话")

    issues: List[str] = []
    written = 0
    seen_names: Set[str] = set()

    with open(OUT_NDJSON, "w", encoding="utf-8") as out:
        for i, row in enumerate(data, start=1):
            if len(row) < len(header):
                row = row + [""] * (len(header) - len(row))
            name = (row[idx_name] if idx_name >= 0 else "").strip()
            course_name = (row[idx_course] if idx_course >= 0 else "").strip()
            date_raw = (row[idx_date] if idx_date >= 0 else "").strip()
            phone_raw = (row[idx_phone] if idx_phone >= 0 else "").strip()

            if not name and not course_name and not phone_raw:
                continue

            if name in seen_names:
                issues.append(f"- 行{header_idx + 1 + i}: 姓名【{name}】 -> duplicate_name_skipped")
                continue
            seen_names.add(name)

            payload_issues: List[str] = []

            enroll_date, date_ok = parse_date(date_raw)
            if not date_ok:
                payload_issues.append("invalid_date")

            phone, phone_issue_flags = enforce_phone_11(phone_raw, name)
            payload_issues.extend(phone_issue_flags)

            course_id, course_ok = map_course_id(course_name)
            if not course_ok:
                payload_issues.append("unknown_course")

            gender, gender_conf = infer_gender(name)
            if gender_conf != "high":
                payload_issues.append(f"gender_{gender_conf}")

            source_id = DEFAULT_SOURCE_ID

            payload = {
                "studentInfo": {
                    "name": name,
                    "gender": gender,
                    "age": DEFAULT_AGE,
                    "phone": phone,
                    "sourceId": source_id,
                    "campusId": DEFAULT_CAMPUS_ID,
                },
                "courseInfoList": [
                    {
                        "courseId": course_id,
                        "courseName": course_name,
                        "courseTypeName": map_course_type(course_name),
                        "enrollDate": enroll_date or None,
                        "fixedScheduleTimes": [],
                        "status": "STUDYING",
                        "coachName": derive_coach_name(course_name),
                    }
                ]
            }

            out.write(json.dumps(payload, ensure_ascii=False) + "\n")
            written += 1

            if payload_issues:
                issues.append(
                    f"- 行{header_idx + 1 + i}: 姓名【{name}】 -> 问题: " + ", ".join(payload_issues)
                )

    with open(OUT_REPORT, "w", encoding="utf-8") as rf:
        rf.write("# 学员创建Payload校验报告\n\n")
        rf.write(f"源文件: `{INPUT_CSV}`\n\n")
        rf.write(f"生成条目: {written}\n\n")
        if issues:
            rf.write("## 存在问题的记录\n")
            rf.write("\n".join(issues) + "\n")
        else:
            rf.write("## 未发现明显问题\n")

    print(f"Wrote {written} entries to {OUT_NDJSON}")
    print(f"Report saved to {OUT_REPORT}")


if __name__ == "__main__":
    main() 