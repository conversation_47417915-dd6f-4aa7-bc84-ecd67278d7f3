
server:
  servlet:
    context-path: /lesson
  port: 8090

spring:
  mvc:
    log-request-details: true
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************************************************************************************************************************
    username: lesson
    password: <PERSON><PERSON><PERSON>@0217
    hikari:
      connection-timeout: 30000
      maximum-pool-size: 10
      minimum-idle: 5
      idle-timeout: 600000
      max-lifetime: 1800000
      auto-commit: true
      pool-name: LessonHikariCP
  redis:
    host: *************
    port: 6379
    database: 1
    password: bg-league--redis-password-dont-change
    timeout: 10000
    lettuce:
      pool:
        max-active: 8
        max-wait: -1
        max-idle: 8
        min-idle: 0

logging:
  level:
    root: INFO
    com.lesson: DEBUG
    org.springframework.web: DEBUG
    org.springframework.security: DEBUG
    org.springframework.web.servlet.DispatcherServlet: DEBUG
    org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping: TRACE
    org.jooq: DEBUG
