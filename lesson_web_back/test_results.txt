开始测试StatisticsController中的所有接口...
================================================
1. 测试学员分析统计接口
------------------------
1.1 获取学员分析统计数据（完整版）
./test_statistics_apis.sh: line 22: jq: command not found
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100    98    0    48  100    50   1403   1462 --:--:-- --:--:-- --:--:--  2882
curl: Failed writing body

1.2 获取学员指标统计
./test_statistics_apis.sh: line 31: jq: command not found
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100    98    0    48  100    50   5431   5658 --:--:-- --:--:-- --:--:-- 12250
curl: Failed writing body

1.3 获取学员增长趋势
./test_statistics_apis.sh: line 40: jq: command not found
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100    98    0    48  100    50   5172   5388 --:--:-- --:--:-- --:--:-- 10888
curl: Failed writing body

1.4 获取学员续费金额趋势
./test_statistics_apis.sh: line 49: jq: command not found
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100    98    0    48  100    50   5762   6003 --:--:-- --:--:-- --:--:-- 12250
curl: Failed writing body

1.5 获取学员来源分布
./test_statistics_apis.sh: line 58: jq: command not found
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100    98    0    48  100    50   5486   5715 --:--:-- --:--:-- --:--:-- 12250
curl: Failed writing body

1.6 获取新增学员来源分布
./test_statistics_apis.sh: line 67: jq: command not found
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100    98    0    48  100    50   5880   6125 --:--:-- --:--:-- --:--:-- 12250
curl: Failed writing body

2. 测试课程分析统计接口
------------------------
2.1 获取课程分析统计数据（完整版）
./test_statistics_apis.sh: line 83: jq: command not found
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100   145    0    48  100    97   5184  10477 --:--:-- --:--:-- --:--:-- 16111
curl: Failed writing body

2.2 获取课程指标统计
./test_statistics_apis.sh: line 92: jq: command not found
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100    98    0    48  100    50   6070   6323 --:--:-- --:--:-- --:--:-- 14000
curl: Failed writing body

2.3 获取课程类型分析
./test_statistics_apis.sh: line 101: jq: command not found
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100    98    0    48  100    50   6239   6499 --:--:-- --:--:-- --:--:-- 14000
curl: Failed writing body

2.4 获取课程销售趋势
./test_statistics_apis.sh: line 110: jq: command not found
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100    98    0    48  100    50   6337   6601 --:--:-- --:--:-- --:--:-- 14000
curl: Failed writing body

2.5 获取课程销售表现
./test_statistics_apis.sh: line 119: jq: command not found
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100    98    0    48  100    50   6223   6482 --:--:-- --:--:-- --:--:-- 14000
curl: Failed writing body

2.6 获取课程销售排行
./test_statistics_apis.sh: line 130: jq: command not found
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100   145    0    48  100    97   7929  16025 --:--:-- --:--:-- --:--:-- 24166
curl: Failed writing body

2.7 获取课程收入分析
./test_statistics_apis.sh: line 139: jq: command not found
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100    98    0    48  100    50   8525   8880 --:--:-- --:--:-- --:--:-- 19600
curl: Failed writing body

2.8 获取课程收入分布
./test_statistics_apis.sh: line 148: jq: command not found
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100    98    0    48  100    50   7576   7892 --:--:-- --:--:-- --:--:-- 16333
curl: Failed writing body

3. 测试教练分析统计接口
------------------------
3.1 获取教练分析统计数据（完整版）
./test_statistics_apis.sh: line 162: jq: command not found
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100    98    0    48  100    50   8605   8963 --:--:-- --:--:-- --:--:-- 19600
curl: Failed writing body

3.2 获取教练指标统计
./test_statistics_apis.sh: line 171: jq: command not found
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100    98    0    48  100    50   7770   8094 --:--:-- --:--:-- --:--:-- 16333
curl: Failed writing body

3.3 获取教练课时趋势
./test_statistics_apis.sh: line 180: jq: command not found
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100    98    0    48  100    50   7159   7458 --:--:-- --:--:-- --:--:-- 16333
curl: Failed writing body

3.4 获取教练TOP5对比
./test_statistics_apis.sh: line 189: jq: command not found
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100    98    0    48  100    50   9099   9478 --:--:-- --:--:-- --:--:-- 19600
curl: Failed writing body

3.5 获取教练类型分布
./test_statistics_apis.sh: line 198: jq: command not found
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100    98    0    48  100    50   9096   9475 --:--:-- --:--:-- --:--:-- 19600
curl: Failed writing body

3.6 获取教练薪资分析
./test_statistics_apis.sh: line 207: jq: command not found
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100    98    0    48  100    50   8643   9004 --:--:-- --:--:-- --:--:-- 19600
curl: Failed writing body

3.7 获取教练绩效排行
./test_statistics_apis.sh: line 216: jq: command not found
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100    98    0    48  100    50   9042   9419 --:--:-- --:--:-- --:--:-- 19600
curl: Failed writing body

4. 测试财务分析统计接口
------------------------
4.1 获取财务分析统计数据（完整版）
./test_statistics_apis.sh: line 230: jq: command not found
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100    98    0    48  100    50   8223   8566 --:--:-- --:--:-- --:--:-- 19600
curl: Failed writing body

4.2 获取财务指标统计
./test_statistics_apis.sh: line 239: jq: command not found
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100    98    0    48  100    50   8438   8790 --:--:-- --:--:-- --:--:-- 19600
curl: Failed writing body

4.3 获取财务收支趋势
./test_statistics_apis.sh: line 248: jq: command not found
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100    98    0    48  100    50   8746   9110 --:--:-- --:--:-- --:--:-- 19600
curl: Failed writing body

4.4 获取财务成本结构
./test_statistics_apis.sh: line 257: jq: command not found
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100    98    0    48  100    50   8622   8981 --:--:-- --:--:-- --:--:-- 19600
curl: Failed writing body

4.5 获取财务趋势
./test_statistics_apis.sh: line 266: jq: command not found
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100    98    0    48  100    50   8311   8658 --:--:-- --:--:-- --:--:-- 19600
curl: Failed writing body

4.6 获取财务收入分析
./test_statistics_apis.sh: line 275: jq: command not found
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100    98    0    48  100    50   7383   7691 --:--:-- --:--:-- --:--:-- 16333
curl: Failed writing body

4.7 获取财务成本分析
./test_statistics_apis.sh: line 284: jq: command not found
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100    98    0    48  100    50   7809   8135 --:--:-- --:--:-- --:--:-- 16333
curl: Failed writing body

4.8 获取财务利润分析
./test_statistics_apis.sh: line 293: jq: command not found
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100    98    0    48  100    50   7662   7982 --:--:-- --:--:-- --:--:-- 16333
curl: Failed writing body

5. 测试新增统计接口
------------------------
5.1 获取学员管理页面统计数据
./test_statistics_apis.sh: line 302: jq: command not found
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100    48    0    48    0     0   7765      0 --:--:-- --:--:-- --:--:--  8000
curl: Failed writing body

5.2 刷新统计数据
./test_statistics_apis.sh: line 306: jq: command not found
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100    48    0    48    0     0   7746      0 --:--:-- --:--:-- --:--:--  8000
curl: Failed writing body

5.3 刷新指定校区统计数据
./test_statistics_apis.sh: line 310: jq: command not found
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100    48    0    48    0     0   7279      0 --:--:-- --:--:-- --:--:--  8000
curl: Failed writing body

测试完成！
