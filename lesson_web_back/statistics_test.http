### 统计接口测试文件
### 基础配置
@baseUrl = http://localhost:8080/lesson
@contentType = application/json

### ==================== 学员分析统计接口测试 ====================

### 1. 获取学员分析统计数据（完整版）
POST {{baseUrl}}/api/statistics/student-analysis
Content-Type: {{contentType}}

{
  "timeType": "MONTHLY",
  "startTime": null,
  "endTime": null,
  "campusId": 1,
  "institutionId": 5
}

### 2. 获取学员指标统计
POST {{baseUrl}}/api/statistics/student/metrics
Content-Type: {{contentType}}

{
  "timeType": "MONTHLY",
  "startTime": null,
  "endTime": null,
  "campusId": 1,
  "institutionId": 5
}

### 3. 获取学员增长趋势
POST {{baseUrl}}/api/statistics/student/growth-trend
Content-Type: {{contentType}}

{
  "timeType": "MONTHLY",
  "startTime": null,
  "endTime": null,
  "campusId": 1,
  "institutionId": 5
}

### 4. 获取学员续费金额趋势
POST {{baseUrl}}/api/statistics/student/renewal-trend
Content-Type: {{contentType}}

{
  "timeType": "MONTHLY",
  "startTime": null,
  "endTime": null,
  "campusId": 1,
  "institutionId": 5
}

### 5. 获取学员来源分布
POST {{baseUrl}}/api/statistics/student/source-distribution
Content-Type: {{contentType}}

{
  "timeType": "MONTHLY",
  "startTime": null,
  "endTime": null,
  "campusId": 1,
  "institutionId": 5
}

### 6. 获取新增学员来源分布
POST {{baseUrl}}/api/statistics/student/new-student-source
Content-Type: {{contentType}}

{
  "timeType": "MONTHLY",
  "startTime": null,
  "endTime": null,
  "campusId": 1,
  "institutionId": 5
}

### ==================== 课程分析统计接口测试 ====================

### 7. 获取课程分析统计数据（完整版）
POST {{baseUrl}}/api/statistics/course-analysis
Content-Type: {{contentType}}

{
  "timeType": "MONTHLY",
  "startTime": null,
  "endTime": null,
  "campusId": 1,
  "institutionId": 5,
  "rankingType": "REVENUE",
  "limit": 10
}

### 8. 获取课程指标统计
POST {{baseUrl}}/api/statistics/course/metrics
Content-Type: {{contentType}}

{
  "timeType": "MONTHLY",
  "startTime": null,
  "endTime": null,
  "campusId": 1,
  "institutionId": 5
}

### 9. 获取课程类型分析
POST {{baseUrl}}/api/statistics/course/type-analysis
Content-Type: {{contentType}}

{
  "timeType": "MONTHLY",
  "startTime": null,
  "endTime": null,
  "campusId": 1,
  "institutionId": 5
}

### 10. 获取课程销售趋势
POST {{baseUrl}}/api/statistics/course/sales-trend
Content-Type: {{contentType}}

{
  "timeType": "MONTHLY",
  "startTime": null,
  "endTime": null,
  "campusId": 1,
  "institutionId": 5
}

### 11. 获取课程销售表现
POST {{baseUrl}}/api/statistics/course/sales-performance
Content-Type: {{contentType}}

{
  "timeType": "MONTHLY",
  "startTime": null,
  "endTime": null,
  "campusId": 1,
  "institutionId": 5
}

### 12. 获取课程销售排行
POST {{baseUrl}}/api/statistics/course/sales-ranking
Content-Type: {{contentType}}

{
  "timeType": "MONTHLY",
  "startTime": null,
  "endTime": null,
  "campusId": 1,
  "institutionId": 5,
  "rankingType": "REVENUE",
  "limit": 10
}

### 13. 获取课程收入分析
POST {{baseUrl}}/api/statistics/course/revenue-analysis
Content-Type: {{contentType}}

{
  "timeType": "MONTHLY",
  "startTime": null,
  "endTime": null,
  "campusId": 1,
  "institutionId": 5
}

### 14. 获取课程收入分布
POST {{baseUrl}}/api/statistics/course/revenue-distribution
Content-Type: {{contentType}}

{
  "timeType": "MONTHLY",
  "startTime": null,
  "endTime": null,
  "campusId": 1,
  "institutionId": 5
}

### ==================== 教练分析统计接口测试 ====================

### 15. 获取教练分析统计数据（完整版）
POST {{baseUrl}}/api/statistics/coach-analysis
Content-Type: {{contentType}}

{
  "timeType": "MONTHLY",
  "startTime": null,
  "endTime": null,
  "campusId": 1,
  "institutionId": 5,
  "rankingType": "ALL",
  "limit": 10
}

### 16. 获取教练绩效指标
POST {{baseUrl}}/api/statistics/coach/metrics
Content-Type: {{contentType}}

{
  "timeType": "MONTHLY",
  "startTime": null,
  "endTime": null,
  "campusId": 1,
  "institutionId": 5
}

### 17. 获取教练课时统计趋势
POST {{baseUrl}}/api/statistics/coach/class-hour-trend
Content-Type: {{contentType}}

{
  "timeType": "MONTHLY",
  "startTime": null,
  "endTime": null,
  "campusId": 1,
  "institutionId": 5
}

### 18. 获取教练TOP5多维度对比
POST {{baseUrl}}/api/statistics/coach/top5-comparison
Content-Type: {{contentType}}

{
  "timeType": "MONTHLY",
  "startTime": null,
  "endTime": null,
  "campusId": 1,
  "institutionId": 5,
  "rankingType": "ALL",
  "limit": 5
}

### 19. 获取教练类型分布
POST {{baseUrl}}/api/statistics/coach/type-distribution
Content-Type: {{contentType}}

{
  "timeType": "MONTHLY",
  "startTime": null,
  "endTime": null,
  "campusId": 1,
  "institutionId": 5
}

### 20. 获取教练薪资分析
POST {{baseUrl}}/api/statistics/coach/salary-analysis
Content-Type: {{contentType}}

{
  "timeType": "MONTHLY",
  "startTime": null,
  "endTime": null,
  "campusId": 1,
  "institutionId": 5
}

### 21. 获取教练绩效排名
POST {{baseUrl}}/api/statistics/coach/performance-ranking
Content-Type: {{contentType}}

{
  "timeType": "MONTHLY",
  "startTime": null,
  "endTime": null,
  "campusId": 1,
  "institutionId": 5,
  "rankingType": "ALL",
  "limit": 10
}

### ==================== 财务分析统计接口测试 ====================

### 22. 获取财务分析统计数据（完整版）
POST {{baseUrl}}/api/statistics/finance-analysis
Content-Type: {{contentType}}

{
  "timeType": "MONTHLY",
  "startTime": null,
  "endTime": null,
  "campusId": 1,
  "institutionId": 5,
  "analysisType": "ALL"
}

### 23. 获取财务核心指标
POST {{baseUrl}}/api/statistics/finance/metrics
Content-Type: {{contentType}}

{
  "timeType": "MONTHLY",
  "startTime": null,
  "endTime": null,
  "campusId": 1,
  "institutionId": 5
}

### 24. 获取收入与成本趋势
POST {{baseUrl}}/api/statistics/finance/revenue-cost-trend
Content-Type: {{contentType}}

{
  "timeType": "MONTHLY",
  "startTime": null,
  "endTime": null,
  "campusId": 1,
  "institutionId": 5
}

### 25. 获取成本结构分析
POST {{baseUrl}}/api/statistics/finance/cost-structure
Content-Type: {{contentType}}

{
  "timeType": "MONTHLY",
  "startTime": null,
  "endTime": null,
  "campusId": 1,
  "institutionId": 5
}

### 26. 获取财务指标趋势
POST {{baseUrl}}/api/statistics/finance/trend
Content-Type: {{contentType}}

{
  "timeType": "MONTHLY",
  "startTime": null,
  "endTime": null,
  "campusId": 1,
  "institutionId": 5
}

### 27. 获取收入分析
POST {{baseUrl}}/api/statistics/finance/revenue-analysis
Content-Type: {{contentType}}

{
  "timeType": "MONTHLY",
  "startTime": null,
  "endTime": null,
  "campusId": 1,
  "institutionId": 5
}

### 28. 获取成本分析
POST {{baseUrl}}/api/statistics/finance/cost-analysis
Content-Type: {{contentType}}

{
  "timeType": "MONTHLY",
  "startTime": null,
  "endTime": null,
  "campusId": 1,
  "institutionId": 5
}

### 29. 获取利润分析
POST {{baseUrl}}/api/statistics/finance/profit-analysis
Content-Type: {{contentType}}

{
  "timeType": "MONTHLY",
  "startTime": null,
  "endTime": null,
  "campusId": 1,
  "institutionId": 5
}

### ==================== 边界条件测试 ====================

### 30. 测试无效的统计类型
POST {{baseUrl}}/api/statistics/student/metrics
Content-Type: {{contentType}}

{
  "timeType": "INVALID_TYPE",
  "campusId": 1,
  "institutionId": 5
}

### 31. 测试缺少必填参数
POST {{baseUrl}}/api/statistics/student/metrics
Content-Type: {{contentType}}

{
  "campusId": 1,
  "institutionId": 5
}

### 32. 测试指定时间范围
POST {{baseUrl}}/api/statistics/student/metrics
Content-Type: {{contentType}}

{
  "timeType": "MONTHLY",
  "startTime": "2024-01-01",
  "endTime": "2024-12-31",
  "campusId": 1,
  "institutionId": 5
}

### 33. 测试不同统计类型
POST {{baseUrl}}/api/statistics/student/metrics
Content-Type: {{contentType}}

{
  "timeType": "WEEKLY",
  "campusId": 1,
  "institutionId": 5
}

### 34. 测试季度统计
POST {{baseUrl}}/api/statistics/student/metrics
Content-Type: {{contentType}}

{
  "timeType": "QUARTERLY",
  "campusId": 1,
  "institutionId": 5
}

### 35. 测试年度统计
POST {{baseUrl}}/api/statistics/student/metrics
Content-Type: {{contentType}}

{
  "timeType": "YEARLY",
  "campusId": 1,
  "institutionId": 5
} 