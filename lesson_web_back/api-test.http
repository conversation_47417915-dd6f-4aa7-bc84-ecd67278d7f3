### 固定课表管理系统 API 测试
### 基础配置
@baseUrl = http://lesson.devtesting.top/lesson
@token = eyJhbGciOiJIUzM4NCJ9.eyJ1c2VySWQiOjYsIm9yZ0lkIjoyLCJpYXQiOjE3NTEwNzMxMTAsImV4cCI6MTc1MTY3NzkxMH0.IXD_qXLuL34_TbbAkvUL4LuXQnZUh5tpWLiatBt7U6RO1rZAlUvp_T2eV7Ybh8KA

### ================================
### 1. 用户认证
### ================================

### 用户登录
POST {{baseUrl}}/api/auth/login
Content-Type: application/json

{
    "phone": "15811384776",
    "password": "12345678"
}

### ================================
### 2. 固定课表接口
### ================================

### 获取固定课表（必须传校区ID）
GET {{baseUrl}}/api/fixed-schedule/list?campusId=3
Authorization: {{token}}

### 按教练ID查询固定课表
GET {{baseUrl}}/api/fixed-schedule/list?coachId=1
Authorization: {{token}}

### 按课程ID查询固定课表
GET {{baseUrl}}/api/fixed-schedule/list?courseId=2
Authorization: {{token}}

### 按课程类型查询固定课表
GET {{baseUrl}}/api/fixed-schedule/list?type=舞蹈
Authorization: {{token}}

### 组合条件查询
GET {{baseUrl}}/api/fixed-schedule/list?coachId=1&courseId=2&type=舞蹈
Authorization: {{token}}

### ================================
### 3. 学员管理接口
### ================================

### 创建学员及课程（包含固定课表）
POST {{baseUrl}}/api/student/create
Authorization: {{token}}
Content-Type: application/json

{
    "studentInfo": {
        "name": "张小明",
        "gender": "男",
        "age": 8,
        "phone": "13800138001",
        "campusId": 1
    },
    "courseInfoList": [
        {
            "courseId": 1,
            "enrollDate": "2024-01-15",
            "status": "STUDYING",
            "fixedScheduleTimes": [
                {
                    "weekday": "周一",
                    "from": "15:00",
                    "to": "16:00"
                },
                {
                    "weekday": "周三",
                    "from": "16:00",
                    "to": "17:00"
                }
            ]
        }
    ]
}

### 查询学员列表
POST {{baseUrl}}/api/student/list
Authorization: {{token}}
Content-Type: application/json

{
    "pageNum": 1,
    "pageSize": 10,
    "courseId": 1,
    "campusId": 1
}

### 获取学员缴费课时缓存信息
GET {{baseUrl}}/api/student/payment-hours?studentId=1&courseId=1
Authorization: {{token}}

### ================================
### 4. 课程管理接口
### ================================

### 创建课程
POST {{baseUrl}}/api/courses/create
Authorization: {{token}}
Content-Type: application/json

{
    "name": "少儿舞蹈课",
    "typeId": 1,
    "unitHours": 1.0,
    "price": 200.00,
    "coachFee": 100.00,
    "campusId": 1,
    "coachIds": [1],
    "description": "适合4-12岁儿童的舞蹈课程"
}

### 查询课程列表
POST {{baseUrl}}/api/courses/list
Authorization: {{token}}
Content-Type: application/json

{
    "pageNum": 1,
    "pageSize": 10,
    "campusId": 1
}

### ================================
### 5. 缴费接口测试
### ================================

### 学员缴费
POST {{baseUrl}}/api/student/payment
Authorization: {{token}}
Content-Type: application/json

{
    "studentId": 1,
    "courseId": 1,
    "paymentAmount": 2000.00,
    "regularHours": 20.0,
    "giftHours": 5.0,
    "paymentMethod": "微信支付",
    "notes": "新学员缴费"
}

### ================================
### 6. 系统管理接口
### ================================

### 获取课程类型常量
GET {{baseUrl}}/api/constants/list?type=COURSE_TYPE
Authorization: {{token}}

### 获取可分配角色列表
GET {{baseUrl}}/api/roles/assignable
Authorization: {{token}}

### ================================
### 7. 财务管理接口
### ================================

### 添加财务记录
POST {{baseUrl}}/api/finance/record
Authorization: {{token}}
Content-Type: application/json

{
    "type": "EXPEND",
    "date": "2024-06-01",
    "item": "学费12121212",
    "amount": 2000.00,
    "categoryId": 40,
    "notes": "2024年6月学费",
    "campusId": 1
}

### 查询财务记录列表
POST {{baseUrl}}/api/finance/list
Authorization: {{token}}
Content-Type: application/json

{
    "transactionType": "EXPEND",
    "startDate": "2024-06-01",
    "endDate": "2024-06-30",
    "campusId": 1,
    "pageNum": 1,
    "pageSize": 10
}

### 查询全部财务记录（不传类型）
POST {{baseUrl}}/api/finance/list
Authorization: {{token}}
Content-Type: application/json

{
    "keyword": "",
    "pageNum": 1,
    "pageSize": 10
}

### ================================
### 8. 打卡消课记录
### ================================

### 打卡消课记录列表
POST {{baseUrl}}/api/attendance/record/list
Authorization: {{token}}
Content-Type: application/json

{
    "pageNum": 1,
    "pageSize": 10
}

### 打卡记录统计
POST {{baseUrl}}/api/attendance/record/stat
Authorization: {{token}}
Content-Type: application/json

{
    "startDate": "2023-06-01",
    "endDate": "2023-06-30"
}

### ================================
### 9. 缴费记录接口测试
### ================================

### 查询缴费记录列表
POST {{baseUrl}}/api/payment/record/list
Authorization: {{token}}
Content-Type: application/json

{
    "keyword": "",
    "courseId": null,
    "lessonType": "",
    "paymentType": "",
    "payType": "",
    "startDate": null,
    "endDate": null,
    "campusId": 1,
    "pageNum": 1,
    "pageSize": 20
}

### 查询缴费统计
POST {{baseUrl}}/api/payment/record/stat
Authorization: {{token}}
Content-Type: application/json

{
    "startDate": "2023-06-01",
    "endDate": "2023-06-30"
}

### 查询缴费统计（全时段）
POST {{baseUrl}}/api/payment/record/stat
Authorization: {{token}}
Content-Type: application/json

{
}

### ================================
### 使用说明
### ================================
# 1. 首先调用登录接口获取token
# 2. 将获取到的token替换上面的 YOUR_JWT_TOKEN
# 3. 根据需要调用相应的接口进行测试
# 4. 注意修改请求参数中的ID值为实际存在的数据 

### 获取学员课程列表
GET {{baseUrl}}/api/student/courses
Authorization: {{token}}

### 获取课程详情
GET {{baseUrl}}/api/student/courses/1
Authorization: {{token}}

### 获取课程总课时
GET {{baseUrl}}/api/student/courses/1/total-hours
Authorization: {{token}}

### 获取课程剩余课时
GET {{baseUrl}}/api/student/courses/1/remaining-hours
Authorization: {{token}}

### 获取课程消费记录
GET {{baseUrl}}/api/student/courses/1/consumption-records
Authorization: {{token}}

### 获取课程考勤记录
GET {{baseUrl}}/api/student/courses/1/attendance-records
Authorization: {{token}}

### 获取课程评价
GET {{baseUrl}}/api/student/courses/1/evaluation
Authorization: {{token}}

### 提交课程评价
POST {{baseUrl}}/api/student/courses/1/evaluation
Authorization: {{token}}
Content-Type: application/json

{
    "rating": 5,
    "comment": "课程非常好，老师讲解很清晰"
}

### 获取课程进度
GET {{baseUrl}}/api/student/courses/1/progress
Authorization: {{token}} 

### 学员分析统计接口测试

### 获取学员分析统计数据 - 月度统计
POST http://localhost:8080/api/statistics/student-analysis
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1
}

### 获取学员分析统计数据 - 周度统计
POST http://localhost:8080/api/statistics/student-analysis
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "timeType": "WEEKLY"
}

### 获取学员分析统计数据 - 季度统计
POST http://localhost:8080/api/statistics/student-analysis
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "timeType": "QUARTERLY"
}

### 获取学员分析统计数据 - 年度统计
POST http://localhost:8080/api/statistics/student-analysis
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "timeType": "YEARLY"
}

### 获取学员分析统计数据 - 无过滤条件
POST http://localhost:8080/api/statistics/student-analysis
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "timeType": "MONTHLY"
}

### 课程分析统计接口测试

### 获取课程分析统计数据 - 月度统计
POST http://localhost:8080/api/statistics/course-analysis
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1
}

### 获取课程分析统计数据 - 周度统计
POST http://localhost:8080/api/statistics/course-analysis
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "timeType": "WEEKLY"
}

### 获取课程分析统计数据 - 季度统计
POST http://localhost:8080/api/statistics/course-analysis
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "timeType": "QUARTERLY"
}

### 获取课程分析统计数据 - 年度统计
POST http://localhost:8080/api/statistics/course-analysis
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "timeType": "YEARLY"
}

### 获取课程分析统计数据 - 无过滤条件
POST http://localhost:8080/api/statistics/course-analysis
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "timeType": "MONTHLY"
} 

### 教练分析统计 - 月度统计
POST http://localhost:8080/api/statistics/coach-analysis
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1
}

### 教练分析统计 - 周度统计
POST http://localhost:8080/api/statistics/coach-analysis
Content-Type: application/json

{
  "timeType": "WEEKLY",
  "campusId": 1,
  "institutionId": 1
}

### 教练分析统计 - 季度统计
POST http://localhost:8080/api/statistics/coach-analysis
Content-Type: application/json

{
  "timeType": "QUARTERLY",
  "campusId": 1,
  "institutionId": 1
}

### 教练分析统计 - 年度统计
POST http://localhost:8080/api/statistics/coach-analysis
Content-Type: application/json

{
  "timeType": "YEARLY",
  "campusId": 1,
  "institutionId": 1
}

### 教练分析统计 - 按课时排名
POST http://localhost:8080/api/statistics/coach-analysis
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1,
  "rankingType": "CLASS_HOURS",
  "limit": 5
}

### 教练分析统计 - 按学员数排名
POST http://localhost:8080/api/statistics/coach-analysis
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1,
  "rankingType": "STUDENTS",
  "limit": 5
}

### 教练分析统计 - 按创收排名
POST http://localhost:8080/api/statistics/coach-analysis
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1,
  "rankingType": "REVENUE",
  "limit": 5
}

### 教练分析统计 - 无过滤条件
POST http://localhost:8080/api/statistics/coach-analysis
Content-Type: application/json

{
  "timeType": "MONTHLY"
} 

### 学员分析统计 - 完整版
POST http://localhost:8080/api/statistics/student-analysis
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1
}

### 学员指标统计
POST http://localhost:8080/api/statistics/student/metrics
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1
}

### 学员增长趋势
POST http://localhost:8080/api/statistics/student/growth-trend
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1
}

### 学员续费金额趋势
POST http://localhost:8080/api/statistics/student/renewal-trend
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1
}

### 学员来源分布
POST http://localhost:8080/api/statistics/student/source-distribution
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1
}

### 新增学员来源分布
POST http://localhost:8080/api/statistics/student/new-student-source
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1
}

### 课程分析统计 - 完整版
POST http://localhost:8080/api/statistics/course-analysis
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1
}

### 课程指标统计
POST http://localhost:8080/api/statistics/course/metrics
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1
}

### 课程类型分析
POST http://localhost:8080/api/statistics/course/type-analysis
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1
}

### 课程销售趋势
POST http://localhost:8080/api/statistics/course/sales-trend
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1
}

### 课程销售表现
POST http://localhost:8080/api/statistics/course/sales-performance
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1
}

### 课程销售排行
POST http://localhost:8080/api/statistics/course/sales-ranking
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1,
  "rankingType": "REVENUE",
  "limit": 10
}

### 课程收入分析
POST http://localhost:8080/api/statistics/course/revenue-analysis
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1
}

### 课程收入分布
POST http://localhost:8080/api/statistics/course/revenue-distribution
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1
}

### 教练分析统计 - 完整版
POST http://localhost:8080/api/statistics/coach-analysis
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1
}

### 教练绩效指标
POST http://localhost:8080/api/statistics/coach/metrics
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1
}

### 教练课时统计趋势
POST http://localhost:8080/api/statistics/coach/class-hour-trend
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1
}

### 教练TOP5多维度对比
POST http://localhost:8080/api/statistics/coach/top5-comparison
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1,
  "rankingType": "CLASS_HOURS",
  "limit": 5
}

### 教练类型分布
POST http://localhost:8080/api/statistics/coach/type-distribution
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1
}

### 教练薪资分析
POST http://localhost:8080/api/statistics/coach/salary-analysis
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1
}

### 教练绩效排名
POST http://localhost:8080/api/statistics/coach/performance-ranking
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1
}

### 学员分析统计 - 周度统计
POST http://localhost:8080/api/statistics/student-analysis
Content-Type: application/json

{
  "timeType": "WEEKLY",
  "campusId": 1,
  "institutionId": 1
}

### 学员分析统计 - 季度统计
POST http://localhost:8080/api/statistics/student-analysis
Content-Type: application/json

{
  "timeType": "QUARTERLY",
  "campusId": 1,
  "institutionId": 1
}

### 学员分析统计 - 年度统计
POST http://localhost:8080/api/statistics/student-analysis
Content-Type: application/json

{
  "timeType": "YEARLY",
  "campusId": 1,
  "institutionId": 1
}

### 学员分析统计 - 无过滤条件
POST http://localhost:8080/api/statistics/student-analysis
Content-Type: application/json

{
  "timeType": "MONTHLY"
}

### 课程分析统计 - 周度统计
POST http://localhost:8080/api/statistics/course-analysis
Content-Type: application/json

{
  "timeType": "WEEKLY",
  "campusId": 1,
  "institutionId": 1
}

### 课程分析统计 - 季度统计
POST http://localhost:8080/api/statistics/course-analysis
Content-Type: application/json

{
  "timeType": "QUARTERLY",
  "campusId": 1,
  "institutionId": 1
}

### 课程分析统计 - 年度统计
POST http://localhost:8080/api/statistics/course-analysis
Content-Type: application/json

{
  "timeType": "YEARLY",
  "campusId": 1,
  "institutionId": 1
}

### 课程分析统计 - 按销量排行
POST http://localhost:8080/api/statistics/course-analysis
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1,
  "rankingType": "SALES_QUANTITY",
  "limit": 10
}

### 课程分析统计 - 按单价排行
POST http://localhost:8080/api/statistics/course-analysis
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1,
  "rankingType": "UNIT_PRICE",
  "limit": 10
}

### 课程分析统计 - 无过滤条件
POST http://localhost:8080/api/statistics/course-analysis
Content-Type: application/json

{
  "timeType": "MONTHLY"
}

### 教练分析统计 - 周度统计
POST http://localhost:8080/api/statistics/coach-analysis
Content-Type: application/json

{
  "timeType": "WEEKLY",
  "campusId": 1,
  "institutionId": 1
}

### 教练分析统计 - 季度统计
POST http://localhost:8080/api/statistics/coach-analysis
Content-Type: application/json

{
  "timeType": "QUARTERLY",
  "campusId": 1,
  "institutionId": 1
}

### 教练分析统计 - 年度统计
POST http://localhost:8080/api/statistics/coach-analysis
Content-Type: application/json

{
  "timeType": "YEARLY",
  "campusId": 1,
  "institutionId": 1
}

### 教练分析统计 - 按课时排名
POST http://localhost:8080/api/statistics/coach-analysis
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1,
  "rankingType": "CLASS_HOURS",
  "limit": 5
}

### 教练分析统计 - 按学员数排名
POST http://localhost:8080/api/statistics/coach-analysis
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1,
  "rankingType": "STUDENTS",
  "limit": 5
}

### 教练分析统计 - 按创收排名
POST http://localhost:8080/api/statistics/coach-analysis
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1,
  "rankingType": "REVENUE",
  "limit": 5
}

### 教练分析统计 - 无过滤条件
POST http://localhost:8080/api/statistics/coach-analysis
Content-Type: application/json

{
  "timeType": "MONTHLY"
} 

### ==================== 财务分析接口测试 ====================

### 财务分析统计 - 完整版
POST http://localhost:8080/api/statistics/finance-analysis
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1
}

### 财务分析统计 - 周度统计
POST http://localhost:8080/api/statistics/finance-analysis
Content-Type: application/json

{
  "timeType": "WEEKLY",
  "campusId": 1,
  "institutionId": 1
}

### 财务分析统计 - 季度统计
POST http://localhost:8080/api/statistics/finance-analysis
Content-Type: application/json

{
  "timeType": "QUARTERLY",
  "campusId": 1,
  "institutionId": 1
}

### 财务分析统计 - 年度统计
POST http://localhost:8080/api/statistics/finance-analysis
Content-Type: application/json

{
  "timeType": "YEARLY",
  "campusId": 1,
  "institutionId": 1
}

### 财务分析统计 - 无过滤条件
POST http://localhost:8080/api/statistics/finance-analysis
Content-Type: application/json

{
  "timeType": "MONTHLY"
}

### 获取财务核心指标
POST http://localhost:8080/api/statistics/finance/metrics
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1
}

### 获取收入与成本趋势
POST http://localhost:8080/api/statistics/finance/revenue-cost-trend
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1
}

### 获取成本结构分析
POST http://localhost:8080/api/statistics/finance/cost-structure
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1
}

### 获取财务指标趋势
POST http://localhost:8080/api/statistics/finance/trend
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1
}

### 获取收入分析
POST http://localhost:8080/api/statistics/finance/revenue-analysis
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1
}

### 获取成本分析
POST http://localhost:8080/api/statistics/finance/cost-analysis
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1
}

### 获取利润分析
POST http://localhost:8080/api/statistics/finance/profit-analysis
Content-Type: application/json

{
  "timeType": "MONTHLY",
  "campusId": 1,
  "institutionId": 1
} 