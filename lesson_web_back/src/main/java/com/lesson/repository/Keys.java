/*
 * This file is generated by jOOQ.
 */
package com.lesson.repository;


import com.lesson.repository.tables.EduCourse;
import com.lesson.repository.tables.EduStudent;
import com.lesson.repository.tables.EduStudentClassTransfer;
import com.lesson.repository.tables.EduStudentCourse;
import com.lesson.repository.tables.EduStudentCourseOperation;
import com.lesson.repository.tables.EduStudentCourseTransfer;
import com.lesson.repository.tables.EduStudentPayment;
import com.lesson.repository.tables.EduStudentRefund;
import com.lesson.repository.tables.SysCampus;
import com.lesson.repository.tables.SysCoach;
import com.lesson.repository.tables.SysCoachCertification;
import com.lesson.repository.tables.SysCoachCourse;
import com.lesson.repository.tables.SysCoachSalary;
import com.lesson.repository.tables.SysConstant;
import com.lesson.repository.tables.SysInstitution;
import com.lesson.repository.tables.SysRole;
import com.lesson.repository.tables.SysRolePermission;
import com.lesson.repository.tables.SysUser;
import com.lesson.repository.tables.records.EduCourseRecord;
import com.lesson.repository.tables.records.EduCourseRecordRecord;
import com.lesson.repository.tables.records.EduStudentClassTransferRecord;
import com.lesson.repository.tables.records.EduStudentCourseOperationRecord;
import com.lesson.repository.tables.records.EduStudentCourseRecord;
import com.lesson.repository.tables.records.EduStudentCourseRecordRecord;
import com.lesson.repository.tables.records.EduStudentCourseTransferRecord;
import com.lesson.repository.tables.records.EduStudentPaymentRecord;
import com.lesson.repository.tables.records.EduStudentRecord;
import com.lesson.repository.tables.records.EduStudentRefundRecord;
import com.lesson.repository.tables.records.SysCampusRecord;
import com.lesson.repository.tables.records.SysCoachCertificationRecord;
import com.lesson.repository.tables.records.SysCoachCourseRecord;
import com.lesson.repository.tables.records.SysCoachRecord;
import com.lesson.repository.tables.records.SysCoachSalaryRecord;
import com.lesson.repository.tables.records.SysConstantRecord;
import com.lesson.repository.tables.records.SysInstitutionRecord;
import com.lesson.repository.tables.records.SysRolePermissionRecord;
import com.lesson.repository.tables.records.SysRoleRecord;
import com.lesson.repository.tables.records.SysUserRecord;

import org.jooq.ForeignKey;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;


/**
 * A class modelling foreign key relationships and constraints of tables in 
 * lesson.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Keys {

    // -------------------------------------------------------------------------
    // UNIQUE and PRIMARY KEY definitions
    // -------------------------------------------------------------------------

    public static final UniqueKey<EduCourseRecord> KEY_EDU_COURSE_IDX_UNIQUE_NAME_CAMPUS_INSTITUTION = Internal.createUniqueKey(EduCourse.EDU_COURSE, DSL.name("KEY_edu_course_idx_unique_name_campus_institution"), new TableField[] { EduCourse.EDU_COURSE.NAME, EduCourse.EDU_COURSE.CAMPUS_ID, EduCourse.EDU_COURSE.INSTITUTION_ID, EduCourse.EDU_COURSE.DELETED }, true);
    public static final UniqueKey<EduCourseRecord> KEY_EDU_COURSE_PRIMARY = Internal.createUniqueKey(EduCourse.EDU_COURSE, DSL.name("KEY_edu_course_PRIMARY"), new TableField[] { EduCourse.EDU_COURSE.ID }, true);
    public static final UniqueKey<EduCourseRecordRecord> KEY_EDU_COURSE_RECORD_PRIMARY = Internal.createUniqueKey(com.lesson.repository.tables.EduCourseRecord.EDU_COURSE_RECORD, DSL.name("KEY_edu_course_record_PRIMARY"), new TableField[] { com.lesson.repository.tables.EduCourseRecord.EDU_COURSE_RECORD.ID }, true);
    public static final UniqueKey<EduStudentRecord> KEY_EDU_STUDENT_PRIMARY = Internal.createUniqueKey(EduStudent.EDU_STUDENT, DSL.name("KEY_edu_student_PRIMARY"), new TableField[] { EduStudent.EDU_STUDENT.ID }, true);
    public static final UniqueKey<EduStudentClassTransferRecord> KEY_EDU_STUDENT_CLASS_TRANSFER_PRIMARY = Internal.createUniqueKey(EduStudentClassTransfer.EDU_STUDENT_CLASS_TRANSFER, DSL.name("KEY_edu_student_class_transfer_PRIMARY"), new TableField[] { EduStudentClassTransfer.EDU_STUDENT_CLASS_TRANSFER.ID }, true);
    public static final UniqueKey<EduStudentCourseRecord> KEY_EDU_STUDENT_COURSE_PRIMARY = Internal.createUniqueKey(EduStudentCourse.EDU_STUDENT_COURSE, DSL.name("KEY_edu_student_course_PRIMARY"), new TableField[] { EduStudentCourse.EDU_STUDENT_COURSE.ID }, true);
    public static final UniqueKey<EduStudentCourseOperationRecord> KEY_EDU_STUDENT_COURSE_OPERATION_PRIMARY = Internal.createUniqueKey(EduStudentCourseOperation.EDU_STUDENT_COURSE_OPERATION, DSL.name("KEY_edu_student_course_operation_PRIMARY"), new TableField[] { EduStudentCourseOperation.EDU_STUDENT_COURSE_OPERATION.ID }, true);
    public static final UniqueKey<EduStudentCourseRecordRecord> KEY_EDU_STUDENT_COURSE_RECORD_PRIMARY = Internal.createUniqueKey(com.lesson.repository.tables.EduStudentCourseRecord.EDU_STUDENT_COURSE_RECORD, DSL.name("KEY_edu_student_course_record_PRIMARY"), new TableField[] { com.lesson.repository.tables.EduStudentCourseRecord.EDU_STUDENT_COURSE_RECORD.ID }, true);
    public static final UniqueKey<EduStudentCourseTransferRecord> KEY_EDU_STUDENT_COURSE_TRANSFER_PRIMARY = Internal.createUniqueKey(EduStudentCourseTransfer.EDU_STUDENT_COURSE_TRANSFER, DSL.name("KEY_edu_student_course_transfer_PRIMARY"), new TableField[] { EduStudentCourseTransfer.EDU_STUDENT_COURSE_TRANSFER.ID }, true);
    public static final UniqueKey<EduStudentPaymentRecord> KEY_EDU_STUDENT_PAYMENT_PRIMARY = Internal.createUniqueKey(EduStudentPayment.EDU_STUDENT_PAYMENT, DSL.name("KEY_edu_student_payment_PRIMARY"), new TableField[] { EduStudentPayment.EDU_STUDENT_PAYMENT.ID }, true);
    public static final UniqueKey<EduStudentRefundRecord> KEY_EDU_STUDENT_REFUND_PRIMARY = Internal.createUniqueKey(EduStudentRefund.EDU_STUDENT_REFUND, DSL.name("KEY_edu_student_refund_PRIMARY"), new TableField[] { EduStudentRefund.EDU_STUDENT_REFUND.ID }, true);
    public static final UniqueKey<SysCampusRecord> KEY_SYS_CAMPUS_PRIMARY = Internal.createUniqueKey(SysCampus.SYS_CAMPUS, DSL.name("KEY_sys_campus_PRIMARY"), new TableField[] { SysCampus.SYS_CAMPUS.ID }, true);
    public static final UniqueKey<SysCoachRecord> KEY_SYS_COACH_PRIMARY = Internal.createUniqueKey(SysCoach.SYS_COACH, DSL.name("KEY_sys_coach_PRIMARY"), new TableField[] { SysCoach.SYS_COACH.ID }, true);
    public static final UniqueKey<SysCoachCertificationRecord> KEY_SYS_COACH_CERTIFICATION_PRIMARY = Internal.createUniqueKey(SysCoachCertification.SYS_COACH_CERTIFICATION, DSL.name("KEY_sys_coach_certification_PRIMARY"), new TableField[] { SysCoachCertification.SYS_COACH_CERTIFICATION.ID }, true);
    public static final UniqueKey<SysCoachCourseRecord> KEY_SYS_COACH_COURSE_PRIMARY = Internal.createUniqueKey(SysCoachCourse.SYS_COACH_COURSE, DSL.name("KEY_sys_coach_course_PRIMARY"), new TableField[] { SysCoachCourse.SYS_COACH_COURSE.ID }, true);
    public static final UniqueKey<SysCoachCourseRecord> KEY_SYS_COACH_COURSE_UK_COACH_COURSE = Internal.createUniqueKey(SysCoachCourse.SYS_COACH_COURSE, DSL.name("KEY_sys_coach_course_uk_coach_course"), new TableField[] { SysCoachCourse.SYS_COACH_COURSE.COACH_ID, SysCoachCourse.SYS_COACH_COURSE.COURSE_ID, SysCoachCourse.SYS_COACH_COURSE.DELETED }, true);
    public static final UniqueKey<SysCoachSalaryRecord> KEY_SYS_COACH_SALARY_PRIMARY = Internal.createUniqueKey(SysCoachSalary.SYS_COACH_SALARY, DSL.name("KEY_sys_coach_salary_PRIMARY"), new TableField[] { SysCoachSalary.SYS_COACH_SALARY.ID }, true);
    public static final UniqueKey<SysConstantRecord> KEY_SYS_CONSTANT_PRIMARY = Internal.createUniqueKey(SysConstant.SYS_CONSTANT, DSL.name("KEY_sys_constant_PRIMARY"), new TableField[] { SysConstant.SYS_CONSTANT.ID }, true);
    public static final UniqueKey<SysConstantRecord> KEY_SYS_CONSTANT_UK_CONSTANT_KEY = Internal.createUniqueKey(SysConstant.SYS_CONSTANT, DSL.name("KEY_sys_constant_uk_constant_key"), new TableField[] { SysConstant.SYS_CONSTANT.CONSTANT_KEY }, true);
    public static final UniqueKey<SysInstitutionRecord> KEY_SYS_INSTITUTION_PRIMARY = Internal.createUniqueKey(SysInstitution.SYS_INSTITUTION, DSL.name("KEY_sys_institution_PRIMARY"), new TableField[] { SysInstitution.SYS_INSTITUTION.ID }, true);
    public static final UniqueKey<SysRoleRecord> KEY_SYS_ROLE_PRIMARY = Internal.createUniqueKey(SysRole.SYS_ROLE, DSL.name("KEY_sys_role_PRIMARY"), new TableField[] { SysRole.SYS_ROLE.ID }, true);
    public static final UniqueKey<SysRoleRecord> KEY_SYS_ROLE_UK_ROLE_NAME = Internal.createUniqueKey(SysRole.SYS_ROLE, DSL.name("KEY_sys_role_uk_role_name"), new TableField[] { SysRole.SYS_ROLE.ROLE_NAME }, true);
    public static final UniqueKey<SysRolePermissionRecord> KEY_SYS_ROLE_PERMISSION_PRIMARY = Internal.createUniqueKey(SysRolePermission.SYS_ROLE_PERMISSION, DSL.name("KEY_sys_role_permission_PRIMARY"), new TableField[] { SysRolePermission.SYS_ROLE_PERMISSION.ID }, true);
    public static final UniqueKey<SysRolePermissionRecord> KEY_SYS_ROLE_PERMISSION_UK_ROLE_PERMISSION = Internal.createUniqueKey(SysRolePermission.SYS_ROLE_PERMISSION, DSL.name("KEY_sys_role_permission_uk_role_permission"), new TableField[] { SysRolePermission.SYS_ROLE_PERMISSION.ROLE_ID, SysRolePermission.SYS_ROLE_PERMISSION.PERMISSION }, true);
    public static final UniqueKey<SysUserRecord> KEY_SYS_USER_PRIMARY = Internal.createUniqueKey(SysUser.SYS_USER, DSL.name("KEY_sys_user_PRIMARY"), new TableField[] { SysUser.SYS_USER.ID }, true);
    public static final UniqueKey<SysUserRecord> KEY_SYS_USER_UK_PHONE = Internal.createUniqueKey(SysUser.SYS_USER, DSL.name("KEY_sys_user_uk_phone"), new TableField[] { SysUser.SYS_USER.PHONE }, true);

    // -------------------------------------------------------------------------
    // FOREIGN KEY definitions
    // -------------------------------------------------------------------------

    public static final ForeignKey<EduStudentCourseRecordRecord, SysConstantRecord> FK_ATTENDANCE_STATUS = Internal.createForeignKey(com.lesson.repository.tables.EduStudentCourseRecord.EDU_STUDENT_COURSE_RECORD, DSL.name("fk_attendance_status"), new TableField[] { com.lesson.repository.tables.EduStudentCourseRecord.EDU_STUDENT_COURSE_RECORD.STATUS_ID }, Keys.KEY_SYS_CONSTANT_PRIMARY, new TableField[] { SysConstant.SYS_CONSTANT.ID }, true);
}
