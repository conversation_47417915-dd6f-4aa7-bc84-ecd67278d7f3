/*
 * This file is generated by jOOQ.
 */
package com.lesson.repository;


import com.lesson.repository.tables.EduCourse;
import com.lesson.repository.tables.EduCourseRecord;
import com.lesson.repository.tables.EduStudent;
import com.lesson.repository.tables.EduStudentClassTransfer;
import com.lesson.repository.tables.EduStudentCourse;
import com.lesson.repository.tables.EduStudentCourseOperation;
import com.lesson.repository.tables.EduStudentCourseRecord;
import com.lesson.repository.tables.EduStudentCourseTransfer;
import com.lesson.repository.tables.EduStudentPayment;
import com.lesson.repository.tables.EduStudentRefund;
import com.lesson.repository.tables.SysCampus;
import com.lesson.repository.tables.SysCoach;
import com.lesson.repository.tables.SysCoachCertification;
import com.lesson.repository.tables.SysCoachCourse;
import com.lesson.repository.tables.SysCoachSalary;
import com.lesson.repository.tables.SysConstant;
import com.lesson.repository.tables.SysInstitution;
import com.lesson.repository.tables.SysRole;
import com.lesson.repository.tables.SysRolePermission;
import com.lesson.repository.tables.SysUser;


/**
 * Convenience access to all tables in lesson.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Tables {

    /**
     * 课程表
     */
    public static final EduCourse EDU_COURSE = EduCourse.EDU_COURSE;

    /**
     * 课程上课记录表
     */
    public static final EduCourseRecord EDU_COURSE_RECORD = EduCourseRecord.EDU_COURSE_RECORD;

    /**
     * 学员表
     */
    public static final EduStudent EDU_STUDENT = EduStudent.EDU_STUDENT;

    /**
     * 学员转班记录表
     */
    public static final EduStudentClassTransfer EDU_STUDENT_CLASS_TRANSFER = EduStudentClassTransfer.EDU_STUDENT_CLASS_TRANSFER;

    /**
     * 学员课程关系表
     */
    public static final EduStudentCourse EDU_STUDENT_COURSE = EduStudentCourse.EDU_STUDENT_COURSE;

    /**
     * 学员课程操作记录表
     */
    public static final EduStudentCourseOperation EDU_STUDENT_COURSE_OPERATION = EduStudentCourseOperation.EDU_STUDENT_COURSE_OPERATION;

    /**
     * 学员课程记录表
     */
    public static final EduStudentCourseRecord EDU_STUDENT_COURSE_RECORD = EduStudentCourseRecord.EDU_STUDENT_COURSE_RECORD;

    /**
     * 学员转课记录表
     */
    public static final EduStudentCourseTransfer EDU_STUDENT_COURSE_TRANSFER = EduStudentCourseTransfer.EDU_STUDENT_COURSE_TRANSFER;

    /**
     * 学员缴费记录表
     */
    public static final EduStudentPayment EDU_STUDENT_PAYMENT = EduStudentPayment.EDU_STUDENT_PAYMENT;

    /**
     * 学员退费记录表
     */
    public static final EduStudentRefund EDU_STUDENT_REFUND = EduStudentRefund.EDU_STUDENT_REFUND;

    /**
     * 校区表
     */
    public static final SysCampus SYS_CAMPUS = SysCampus.SYS_CAMPUS;

    /**
     * 教练表
     */
    public static final SysCoach SYS_COACH = SysCoach.SYS_COACH;

    /**
     * 教练证书表
     */
    public static final SysCoachCertification SYS_COACH_CERTIFICATION = SysCoachCertification.SYS_COACH_CERTIFICATION;

    /**
     * 教练课程关联表
     */
    public static final SysCoachCourse SYS_COACH_COURSE = SysCoachCourse.SYS_COACH_COURSE;

    /**
     * 教练薪资表
     */
    public static final SysCoachSalary SYS_COACH_SALARY = SysCoachSalary.SYS_COACH_SALARY;

    /**
     * 系统常量表
     */
    public static final SysConstant SYS_CONSTANT = SysConstant.SYS_CONSTANT;

    /**
     * 机构表
     */
    public static final SysInstitution SYS_INSTITUTION = SysInstitution.SYS_INSTITUTION;

    /**
     * 系统角色表
     */
    public static final SysRole SYS_ROLE = SysRole.SYS_ROLE;

    /**
     * 角色权限关联表
     */
    public static final SysRolePermission SYS_ROLE_PERMISSION = SysRolePermission.SYS_ROLE_PERMISSION;

    /**
     * 系统用户表
     */
    public static final SysUser SYS_USER = SysUser.SYS_USER;
}
