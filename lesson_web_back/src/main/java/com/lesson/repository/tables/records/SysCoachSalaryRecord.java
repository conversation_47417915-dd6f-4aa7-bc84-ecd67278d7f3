/*
 * This file is generated by jOOQ.
 */
package com.lesson.repository.tables.records;


import com.lesson.repository.tables.SysCoachSalary;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record12;
import org.jooq.Row12;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 教练薪资表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SysCoachSalaryRecord extends UpdatableRecordImpl<SysCoachSalaryRecord> implements Record12<Long, Long, BigDecimal, BigDecimal, BigDecimal, BigDecimal, BigDecimal, BigDecimal, LocalDate, LocalDateTime, LocalDateTime, Integer> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lesson.sys_coach_salary.id</code>. 主键ID
     */
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>lesson.sys_coach_salary.id</code>. 主键ID
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>lesson.sys_coach_salary.coach_id</code>. 关联教练ID
     */
    public void setCoachId(Long value) {
        set(1, value);
    }

    /**
     * Getter for <code>lesson.sys_coach_salary.coach_id</code>. 关联教练ID
     */
    public Long getCoachId() {
        return (Long) get(1);
    }

    /**
     * Setter for <code>lesson.sys_coach_salary.base_salary</code>. 基本工资
     */
    public void setBaseSalary(BigDecimal value) {
        set(2, value);
    }

    /**
     * Getter for <code>lesson.sys_coach_salary.base_salary</code>. 基本工资
     */
    public BigDecimal getBaseSalary() {
        return (BigDecimal) get(2);
    }

    /**
     * Setter for <code>lesson.sys_coach_salary.social_insurance</code>. 社保费
     */
    public void setSocialInsurance(BigDecimal value) {
        set(3, value);
    }

    /**
     * Getter for <code>lesson.sys_coach_salary.social_insurance</code>. 社保费
     */
    public BigDecimal getSocialInsurance() {
        return (BigDecimal) get(3);
    }

    /**
     * Setter for <code>lesson.sys_coach_salary.class_fee</code>. 课时费
     */
    public void setClassFee(BigDecimal value) {
        set(4, value);
    }

    /**
     * Getter for <code>lesson.sys_coach_salary.class_fee</code>. 课时费
     */
    public BigDecimal getClassFee() {
        return (BigDecimal) get(4);
    }

    /**
     * Setter for <code>lesson.sys_coach_salary.performance_bonus</code>. 绩效奖金
     */
    public void setPerformanceBonus(BigDecimal value) {
        set(5, value);
    }

    /**
     * Getter for <code>lesson.sys_coach_salary.performance_bonus</code>. 绩效奖金
     */
    public BigDecimal getPerformanceBonus() {
        return (BigDecimal) get(5);
    }

    /**
     * Setter for <code>lesson.sys_coach_salary.commission</code>. 提成百分比
     */
    public void setCommission(BigDecimal value) {
        set(6, value);
    }

    /**
     * Getter for <code>lesson.sys_coach_salary.commission</code>. 提成百分比
     */
    public BigDecimal getCommission() {
        return (BigDecimal) get(6);
    }

    /**
     * Setter for <code>lesson.sys_coach_salary.dividend</code>. 分红
     */
    public void setDividend(BigDecimal value) {
        set(7, value);
    }

    /**
     * Getter for <code>lesson.sys_coach_salary.dividend</code>. 分红
     */
    public BigDecimal getDividend() {
        return (BigDecimal) get(7);
    }

    /**
     * Setter for <code>lesson.sys_coach_salary.effective_date</code>. 生效日期
     */
    public void setEffectiveDate(LocalDate value) {
        set(8, value);
    }

    /**
     * Getter for <code>lesson.sys_coach_salary.effective_date</code>. 生效日期
     */
    public LocalDate getEffectiveDate() {
        return (LocalDate) get(8);
    }

    /**
     * Setter for <code>lesson.sys_coach_salary.created_time</code>. 创建时间
     */
    public void setCreatedTime(LocalDateTime value) {
        set(9, value);
    }

    /**
     * Getter for <code>lesson.sys_coach_salary.created_time</code>. 创建时间
     */
    public LocalDateTime getCreatedTime() {
        return (LocalDateTime) get(9);
    }

    /**
     * Setter for <code>lesson.sys_coach_salary.update_time</code>. 更新时间
     */
    public void setUpdateTime(LocalDateTime value) {
        set(10, value);
    }

    /**
     * Getter for <code>lesson.sys_coach_salary.update_time</code>. 更新时间
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(10);
    }

    /**
     * Setter for <code>lesson.sys_coach_salary.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public void setDeleted(Integer value) {
        set(11, value);
    }

    /**
     * Getter for <code>lesson.sys_coach_salary.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public Integer getDeleted() {
        return (Integer) get(11);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record12 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row12<Long, Long, BigDecimal, BigDecimal, BigDecimal, BigDecimal, BigDecimal, BigDecimal, LocalDate, LocalDateTime, LocalDateTime, Integer> fieldsRow() {
        return (Row12) super.fieldsRow();
    }

    @Override
    public Row12<Long, Long, BigDecimal, BigDecimal, BigDecimal, BigDecimal, BigDecimal, BigDecimal, LocalDate, LocalDateTime, LocalDateTime, Integer> valuesRow() {
        return (Row12) super.valuesRow();
    }

    @Override
    public Field<Long> field1() {
        return SysCoachSalary.SYS_COACH_SALARY.ID;
    }

    @Override
    public Field<Long> field2() {
        return SysCoachSalary.SYS_COACH_SALARY.COACH_ID;
    }

    @Override
    public Field<BigDecimal> field3() {
        return SysCoachSalary.SYS_COACH_SALARY.BASE_SALARY;
    }

    @Override
    public Field<BigDecimal> field4() {
        return SysCoachSalary.SYS_COACH_SALARY.SOCIAL_INSURANCE;
    }

    @Override
    public Field<BigDecimal> field5() {
        return SysCoachSalary.SYS_COACH_SALARY.CLASS_FEE;
    }

    @Override
    public Field<BigDecimal> field6() {
        return SysCoachSalary.SYS_COACH_SALARY.PERFORMANCE_BONUS;
    }

    @Override
    public Field<BigDecimal> field7() {
        return SysCoachSalary.SYS_COACH_SALARY.COMMISSION;
    }

    @Override
    public Field<BigDecimal> field8() {
        return SysCoachSalary.SYS_COACH_SALARY.DIVIDEND;
    }

    @Override
    public Field<LocalDate> field9() {
        return SysCoachSalary.SYS_COACH_SALARY.EFFECTIVE_DATE;
    }

    @Override
    public Field<LocalDateTime> field10() {
        return SysCoachSalary.SYS_COACH_SALARY.CREATED_TIME;
    }

    @Override
    public Field<LocalDateTime> field11() {
        return SysCoachSalary.SYS_COACH_SALARY.UPDATE_TIME;
    }

    @Override
    public Field<Integer> field12() {
        return SysCoachSalary.SYS_COACH_SALARY.DELETED;
    }

    @Override
    public Long component1() {
        return getId();
    }

    @Override
    public Long component2() {
        return getCoachId();
    }

    @Override
    public BigDecimal component3() {
        return getBaseSalary();
    }

    @Override
    public BigDecimal component4() {
        return getSocialInsurance();
    }

    @Override
    public BigDecimal component5() {
        return getClassFee();
    }

    @Override
    public BigDecimal component6() {
        return getPerformanceBonus();
    }

    @Override
    public BigDecimal component7() {
        return getCommission();
    }

    @Override
    public BigDecimal component8() {
        return getDividend();
    }

    @Override
    public LocalDate component9() {
        return getEffectiveDate();
    }

    @Override
    public LocalDateTime component10() {
        return getCreatedTime();
    }

    @Override
    public LocalDateTime component11() {
        return getUpdateTime();
    }

    @Override
    public Integer component12() {
        return getDeleted();
    }

    @Override
    public Long value1() {
        return getId();
    }

    @Override
    public Long value2() {
        return getCoachId();
    }

    @Override
    public BigDecimal value3() {
        return getBaseSalary();
    }

    @Override
    public BigDecimal value4() {
        return getSocialInsurance();
    }

    @Override
    public BigDecimal value5() {
        return getClassFee();
    }

    @Override
    public BigDecimal value6() {
        return getPerformanceBonus();
    }

    @Override
    public BigDecimal value7() {
        return getCommission();
    }

    @Override
    public BigDecimal value8() {
        return getDividend();
    }

    @Override
    public LocalDate value9() {
        return getEffectiveDate();
    }

    @Override
    public LocalDateTime value10() {
        return getCreatedTime();
    }

    @Override
    public LocalDateTime value11() {
        return getUpdateTime();
    }

    @Override
    public Integer value12() {
        return getDeleted();
    }

    @Override
    public SysCoachSalaryRecord value1(Long value) {
        setId(value);
        return this;
    }

    @Override
    public SysCoachSalaryRecord value2(Long value) {
        setCoachId(value);
        return this;
    }

    @Override
    public SysCoachSalaryRecord value3(BigDecimal value) {
        setBaseSalary(value);
        return this;
    }

    @Override
    public SysCoachSalaryRecord value4(BigDecimal value) {
        setSocialInsurance(value);
        return this;
    }

    @Override
    public SysCoachSalaryRecord value5(BigDecimal value) {
        setClassFee(value);
        return this;
    }

    @Override
    public SysCoachSalaryRecord value6(BigDecimal value) {
        setPerformanceBonus(value);
        return this;
    }

    @Override
    public SysCoachSalaryRecord value7(BigDecimal value) {
        setCommission(value);
        return this;
    }

    @Override
    public SysCoachSalaryRecord value8(BigDecimal value) {
        setDividend(value);
        return this;
    }

    @Override
    public SysCoachSalaryRecord value9(LocalDate value) {
        setEffectiveDate(value);
        return this;
    }

    @Override
    public SysCoachSalaryRecord value10(LocalDateTime value) {
        setCreatedTime(value);
        return this;
    }

    @Override
    public SysCoachSalaryRecord value11(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public SysCoachSalaryRecord value12(Integer value) {
        setDeleted(value);
        return this;
    }

    @Override
    public SysCoachSalaryRecord values(Long value1, Long value2, BigDecimal value3, BigDecimal value4, BigDecimal value5, BigDecimal value6, BigDecimal value7, BigDecimal value8, LocalDate value9, LocalDateTime value10, LocalDateTime value11, Integer value12) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached SysCoachSalaryRecord
     */
    public SysCoachSalaryRecord() {
        super(SysCoachSalary.SYS_COACH_SALARY);
    }

    /**
     * Create a detached, initialised SysCoachSalaryRecord
     */
    public SysCoachSalaryRecord(Long id, Long coachId, BigDecimal baseSalary, BigDecimal socialInsurance, BigDecimal classFee, BigDecimal performanceBonus, BigDecimal commission, BigDecimal dividend, LocalDate effectiveDate, LocalDateTime createdTime, LocalDateTime updateTime, Integer deleted) {
        super(SysCoachSalary.SYS_COACH_SALARY);

        setId(id);
        setCoachId(coachId);
        setBaseSalary(baseSalary);
        setSocialInsurance(socialInsurance);
        setClassFee(classFee);
        setPerformanceBonus(performanceBonus);
        setCommission(commission);
        setDividend(dividend);
        setEffectiveDate(effectiveDate);
        setCreatedTime(createdTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
    }
}
