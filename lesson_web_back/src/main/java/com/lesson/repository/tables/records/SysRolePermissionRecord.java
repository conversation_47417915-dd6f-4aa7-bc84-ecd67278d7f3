/*
 * This file is generated by jOOQ.
 */
package com.lesson.repository.tables.records;


import com.lesson.repository.tables.SysRolePermission;

import java.time.LocalDateTime;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record6;
import org.jooq.Row6;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 角色权限关联表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SysRolePermissionRecord extends UpdatableRecordImpl<SysRolePermissionRecord> implements Record6<Long, Long, String, LocalDateTime, LocalDateTime, Integer> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lesson.sys_role_permission.id</code>. 主键ID
     */
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>lesson.sys_role_permission.id</code>. 主键ID
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>lesson.sys_role_permission.role_id</code>. 角色ID
     */
    public void setRoleId(Long value) {
        set(1, value);
    }

    /**
     * Getter for <code>lesson.sys_role_permission.role_id</code>. 角色ID
     */
    public Long getRoleId() {
        return (Long) get(1);
    }

    /**
     * Setter for <code>lesson.sys_role_permission.permission</code>. 权限标识
     */
    public void setPermission(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>lesson.sys_role_permission.permission</code>. 权限标识
     */
    public String getPermission() {
        return (String) get(2);
    }

    /**
     * Setter for <code>lesson.sys_role_permission.created_time</code>. 创建时间
     */
    public void setCreatedTime(LocalDateTime value) {
        set(3, value);
    }

    /**
     * Getter for <code>lesson.sys_role_permission.created_time</code>. 创建时间
     */
    public LocalDateTime getCreatedTime() {
        return (LocalDateTime) get(3);
    }

    /**
     * Setter for <code>lesson.sys_role_permission.update_time</code>. 更新时间
     */
    public void setUpdateTime(LocalDateTime value) {
        set(4, value);
    }

    /**
     * Getter for <code>lesson.sys_role_permission.update_time</code>. 更新时间
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(4);
    }

    /**
     * Setter for <code>lesson.sys_role_permission.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public void setDeleted(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>lesson.sys_role_permission.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public Integer getDeleted() {
        return (Integer) get(5);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record6 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row6<Long, Long, String, LocalDateTime, LocalDateTime, Integer> fieldsRow() {
        return (Row6) super.fieldsRow();
    }

    @Override
    public Row6<Long, Long, String, LocalDateTime, LocalDateTime, Integer> valuesRow() {
        return (Row6) super.valuesRow();
    }

    @Override
    public Field<Long> field1() {
        return SysRolePermission.SYS_ROLE_PERMISSION.ID;
    }

    @Override
    public Field<Long> field2() {
        return SysRolePermission.SYS_ROLE_PERMISSION.ROLE_ID;
    }

    @Override
    public Field<String> field3() {
        return SysRolePermission.SYS_ROLE_PERMISSION.PERMISSION;
    }

    @Override
    public Field<LocalDateTime> field4() {
        return SysRolePermission.SYS_ROLE_PERMISSION.CREATED_TIME;
    }

    @Override
    public Field<LocalDateTime> field5() {
        return SysRolePermission.SYS_ROLE_PERMISSION.UPDATE_TIME;
    }

    @Override
    public Field<Integer> field6() {
        return SysRolePermission.SYS_ROLE_PERMISSION.DELETED;
    }

    @Override
    public Long component1() {
        return getId();
    }

    @Override
    public Long component2() {
        return getRoleId();
    }

    @Override
    public String component3() {
        return getPermission();
    }

    @Override
    public LocalDateTime component4() {
        return getCreatedTime();
    }

    @Override
    public LocalDateTime component5() {
        return getUpdateTime();
    }

    @Override
    public Integer component6() {
        return getDeleted();
    }

    @Override
    public Long value1() {
        return getId();
    }

    @Override
    public Long value2() {
        return getRoleId();
    }

    @Override
    public String value3() {
        return getPermission();
    }

    @Override
    public LocalDateTime value4() {
        return getCreatedTime();
    }

    @Override
    public LocalDateTime value5() {
        return getUpdateTime();
    }

    @Override
    public Integer value6() {
        return getDeleted();
    }

    @Override
    public SysRolePermissionRecord value1(Long value) {
        setId(value);
        return this;
    }

    @Override
    public SysRolePermissionRecord value2(Long value) {
        setRoleId(value);
        return this;
    }

    @Override
    public SysRolePermissionRecord value3(String value) {
        setPermission(value);
        return this;
    }

    @Override
    public SysRolePermissionRecord value4(LocalDateTime value) {
        setCreatedTime(value);
        return this;
    }

    @Override
    public SysRolePermissionRecord value5(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public SysRolePermissionRecord value6(Integer value) {
        setDeleted(value);
        return this;
    }

    @Override
    public SysRolePermissionRecord values(Long value1, Long value2, String value3, LocalDateTime value4, LocalDateTime value5, Integer value6) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached SysRolePermissionRecord
     */
    public SysRolePermissionRecord() {
        super(SysRolePermission.SYS_ROLE_PERMISSION);
    }

    /**
     * Create a detached, initialised SysRolePermissionRecord
     */
    public SysRolePermissionRecord(Long id, Long roleId, String permission, LocalDateTime createdTime, LocalDateTime updateTime, Integer deleted) {
        super(SysRolePermission.SYS_ROLE_PERMISSION);

        setId(id);
        setRoleId(roleId);
        setPermission(permission);
        setCreatedTime(createdTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
    }
}
