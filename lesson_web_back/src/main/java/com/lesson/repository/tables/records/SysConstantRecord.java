/*
 * This file is generated by jOOQ.
 */
package com.lesson.repository.tables.records;


import com.lesson.repository.tables.SysConstant;

import java.time.LocalDateTime;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record9;
import org.jooq.Row9;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 系统常量表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SysConstantRecord extends UpdatableRecordImpl<SysConstantRecord> implements Record9<Long, String, String, String, String, Integer, LocalDateTime, LocalDateTime, Integer> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lesson.sys_constant.id</code>. 主键ID
     */
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>lesson.sys_constant.id</code>. 主键ID
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>lesson.sys_constant.constant_key</code>. 常量键
     */
    public void setConstantKey(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>lesson.sys_constant.constant_key</code>. 常量键
     */
    public String getConstantKey() {
        return (String) get(1);
    }

    /**
     * Setter for <code>lesson.sys_constant.constant_value</code>. 常量值
     */
    public void setConstantValue(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>lesson.sys_constant.constant_value</code>. 常量值
     */
    public String getConstantValue() {
        return (String) get(2);
    }

    /**
     * Setter for <code>lesson.sys_constant.description</code>. 描述
     */
    public void setDescription(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>lesson.sys_constant.description</code>. 描述
     */
    public String getDescription() {
        return (String) get(3);
    }

    /**
     * Setter for <code>lesson.sys_constant.type</code>. 常量类型：SYSTEM-系统常量，BUSINESS-业务常量
     */
    public void setType(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>lesson.sys_constant.type</code>. 常量类型：SYSTEM-系统常量，BUSINESS-业务常量
     */
    public String getType() {
        return (String) get(4);
    }

    /**
     * Setter for <code>lesson.sys_constant.status</code>. 状态：0-禁用，1-启用
     */
    public void setStatus(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>lesson.sys_constant.status</code>. 状态：0-禁用，1-启用
     */
    public Integer getStatus() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>lesson.sys_constant.created_time</code>. 创建时间
     */
    public void setCreatedTime(LocalDateTime value) {
        set(6, value);
    }

    /**
     * Getter for <code>lesson.sys_constant.created_time</code>. 创建时间
     */
    public LocalDateTime getCreatedTime() {
        return (LocalDateTime) get(6);
    }

    /**
     * Setter for <code>lesson.sys_constant.update_time</code>. 更新时间
     */
    public void setUpdateTime(LocalDateTime value) {
        set(7, value);
    }

    /**
     * Getter for <code>lesson.sys_constant.update_time</code>. 更新时间
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(7);
    }

    /**
     * Setter for <code>lesson.sys_constant.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public void setDeleted(Integer value) {
        set(8, value);
    }

    /**
     * Getter for <code>lesson.sys_constant.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public Integer getDeleted() {
        return (Integer) get(8);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record9 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row9<Long, String, String, String, String, Integer, LocalDateTime, LocalDateTime, Integer> fieldsRow() {
        return (Row9) super.fieldsRow();
    }

    @Override
    public Row9<Long, String, String, String, String, Integer, LocalDateTime, LocalDateTime, Integer> valuesRow() {
        return (Row9) super.valuesRow();
    }

    @Override
    public Field<Long> field1() {
        return SysConstant.SYS_CONSTANT.ID;
    }

    @Override
    public Field<String> field2() {
        return SysConstant.SYS_CONSTANT.CONSTANT_KEY;
    }

    @Override
    public Field<String> field3() {
        return SysConstant.SYS_CONSTANT.CONSTANT_VALUE;
    }

    @Override
    public Field<String> field4() {
        return SysConstant.SYS_CONSTANT.DESCRIPTION;
    }

    @Override
    public Field<String> field5() {
        return SysConstant.SYS_CONSTANT.TYPE;
    }

    @Override
    public Field<Integer> field6() {
        return SysConstant.SYS_CONSTANT.STATUS;
    }

    @Override
    public Field<LocalDateTime> field7() {
        return SysConstant.SYS_CONSTANT.CREATED_TIME;
    }

    @Override
    public Field<LocalDateTime> field8() {
        return SysConstant.SYS_CONSTANT.UPDATE_TIME;
    }

    @Override
    public Field<Integer> field9() {
        return SysConstant.SYS_CONSTANT.DELETED;
    }

    @Override
    public Long component1() {
        return getId();
    }

    @Override
    public String component2() {
        return getConstantKey();
    }

    @Override
    public String component3() {
        return getConstantValue();
    }

    @Override
    public String component4() {
        return getDescription();
    }

    @Override
    public String component5() {
        return getType();
    }

    @Override
    public Integer component6() {
        return getStatus();
    }

    @Override
    public LocalDateTime component7() {
        return getCreatedTime();
    }

    @Override
    public LocalDateTime component8() {
        return getUpdateTime();
    }

    @Override
    public Integer component9() {
        return getDeleted();
    }

    @Override
    public Long value1() {
        return getId();
    }

    @Override
    public String value2() {
        return getConstantKey();
    }

    @Override
    public String value3() {
        return getConstantValue();
    }

    @Override
    public String value4() {
        return getDescription();
    }

    @Override
    public String value5() {
        return getType();
    }

    @Override
    public Integer value6() {
        return getStatus();
    }

    @Override
    public LocalDateTime value7() {
        return getCreatedTime();
    }

    @Override
    public LocalDateTime value8() {
        return getUpdateTime();
    }

    @Override
    public Integer value9() {
        return getDeleted();
    }

    @Override
    public SysConstantRecord value1(Long value) {
        setId(value);
        return this;
    }

    @Override
    public SysConstantRecord value2(String value) {
        setConstantKey(value);
        return this;
    }

    @Override
    public SysConstantRecord value3(String value) {
        setConstantValue(value);
        return this;
    }

    @Override
    public SysConstantRecord value4(String value) {
        setDescription(value);
        return this;
    }

    @Override
    public SysConstantRecord value5(String value) {
        setType(value);
        return this;
    }

    @Override
    public SysConstantRecord value6(Integer value) {
        setStatus(value);
        return this;
    }

    @Override
    public SysConstantRecord value7(LocalDateTime value) {
        setCreatedTime(value);
        return this;
    }

    @Override
    public SysConstantRecord value8(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public SysConstantRecord value9(Integer value) {
        setDeleted(value);
        return this;
    }

    @Override
    public SysConstantRecord values(Long value1, String value2, String value3, String value4, String value5, Integer value6, LocalDateTime value7, LocalDateTime value8, Integer value9) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached SysConstantRecord
     */
    public SysConstantRecord() {
        super(SysConstant.SYS_CONSTANT);
    }

    /**
     * Create a detached, initialised SysConstantRecord
     */
    public SysConstantRecord(Long id, String constantKey, String constantValue, String description, String type, Integer status, LocalDateTime createdTime, LocalDateTime updateTime, Integer deleted) {
        super(SysConstant.SYS_CONSTANT);

        setId(id);
        setConstantKey(constantKey);
        setConstantValue(constantValue);
        setDescription(description);
        setType(type);
        setStatus(status);
        setCreatedTime(createdTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
    }
}
