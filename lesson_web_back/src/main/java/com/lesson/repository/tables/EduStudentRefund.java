/*
 * This file is generated by jOOQ.
 */
package com.lesson.repository.tables;


import com.lesson.repository.Indexes;
import com.lesson.repository.Keys;
import com.lesson.repository.Lesson;
import com.lesson.repository.tables.records.EduStudentRefundRecord;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Identity;
import org.jooq.Index;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row14;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 学员退费记录表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class EduStudentRefund extends TableImpl<EduStudentRefundRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>lesson.edu_student_refund</code>
     */
    public static final EduStudentRefund EDU_STUDENT_REFUND = new EduStudentRefund();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<EduStudentRefundRecord> getRecordType() {
        return EduStudentRefundRecord.class;
    }

    /**
     * The column <code>lesson.edu_student_refund.id</code>. 记录ID
     */
    public final TableField<EduStudentRefundRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "记录ID");

    /**
     * The column <code>lesson.edu_student_refund.student_id</code>. 学员ID
     */
    public final TableField<EduStudentRefundRecord, String> STUDENT_ID = createField(DSL.name("student_id"), SQLDataType.VARCHAR(32).nullable(false), this, "学员ID");

    /**
     * The column <code>lesson.edu_student_refund.course_id</code>. 课程ID
     */
    public final TableField<EduStudentRefundRecord, String> COURSE_ID = createField(DSL.name("course_id"), SQLDataType.VARCHAR(32).nullable(false), this, "课程ID");

    /**
     * The column <code>lesson.edu_student_refund.refund_hours</code>. 退课课时
     */
    public final TableField<EduStudentRefundRecord, BigDecimal> REFUND_HOURS = createField(DSL.name("refund_hours"), SQLDataType.DECIMAL(10, 2).nullable(false), this, "退课课时");

    /**
     * The column <code>lesson.edu_student_refund.refund_amount</code>. 退款金额
     */
    public final TableField<EduStudentRefundRecord, BigDecimal> REFUND_AMOUNT = createField(DSL.name("refund_amount"), SQLDataType.DECIMAL(10, 2).nullable(false), this, "退款金额");

    /**
     * The column <code>lesson.edu_student_refund.handling_fee</code>. 手续费
     */
    public final TableField<EduStudentRefundRecord, BigDecimal> HANDLING_FEE = createField(DSL.name("handling_fee"), SQLDataType.DECIMAL(10, 2).nullable(false).defaultValue(DSL.inline("0.00", SQLDataType.DECIMAL)), this, "手续费");

    /**
     * The column <code>lesson.edu_student_refund.deduction_amount</code>. 其他费用扣除
     */
    public final TableField<EduStudentRefundRecord, BigDecimal> DEDUCTION_AMOUNT = createField(DSL.name("deduction_amount"), SQLDataType.DECIMAL(10, 2).nullable(false).defaultValue(DSL.inline("0.00", SQLDataType.DECIMAL)), this, "其他费用扣除");

    /**
     * The column <code>lesson.edu_student_refund.actual_refund</code>. 实际退款金额
     */
    public final TableField<EduStudentRefundRecord, BigDecimal> ACTUAL_REFUND = createField(DSL.name("actual_refund"), SQLDataType.DECIMAL(10, 2).nullable(false), this, "实际退款金额");

    /**
     * The column <code>lesson.edu_student_refund.reason</code>. 退费原因
     */
    public final TableField<EduStudentRefundRecord, String> REASON = createField(DSL.name("reason"), SQLDataType.VARCHAR(500).nullable(false), this, "退费原因");

    /**
     * The column <code>lesson.edu_student_refund.campus_id</code>. 校区ID
     */
    public final TableField<EduStudentRefundRecord, Long> CAMPUS_ID = createField(DSL.name("campus_id"), SQLDataType.BIGINT.nullable(false), this, "校区ID");

    /**
     * The column <code>lesson.edu_student_refund.institution_id</code>. 机构ID
     */
    public final TableField<EduStudentRefundRecord, Long> INSTITUTION_ID = createField(DSL.name("institution_id"), SQLDataType.BIGINT.nullable(false), this, "机构ID");

    /**
     * The column <code>lesson.edu_student_refund.created_time</code>. 创建时间
     */
    public final TableField<EduStudentRefundRecord, LocalDateTime> CREATED_TIME = createField(DSL.name("created_time"), SQLDataType.LOCALDATETIME(0).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "创建时间");

    /**
     * The column <code>lesson.edu_student_refund.update_time</code>. 更新时间
     */
    public final TableField<EduStudentRefundRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(0).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "更新时间");

    /**
     * The column <code>lesson.edu_student_refund.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public final TableField<EduStudentRefundRecord, Integer> DELETED = createField(DSL.name("deleted"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否删除：0-未删除，1-已删除");

    private EduStudentRefund(Name alias, Table<EduStudentRefundRecord> aliased) {
        this(alias, aliased, null);
    }

    private EduStudentRefund(Name alias, Table<EduStudentRefundRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("学员退费记录表"), TableOptions.table());
    }

    /**
     * Create an aliased <code>lesson.edu_student_refund</code> table reference
     */
    public EduStudentRefund(String alias) {
        this(DSL.name(alias), EDU_STUDENT_REFUND);
    }

    /**
     * Create an aliased <code>lesson.edu_student_refund</code> table reference
     */
    public EduStudentRefund(Name alias) {
        this(alias, EDU_STUDENT_REFUND);
    }

    /**
     * Create a <code>lesson.edu_student_refund</code> table reference
     */
    public EduStudentRefund() {
        this(DSL.name("edu_student_refund"), null);
    }

    public <O extends Record> EduStudentRefund(Table<O> child, ForeignKey<O, EduStudentRefundRecord> key) {
        super(child, key, EDU_STUDENT_REFUND);
    }

    @Override
    public Schema getSchema() {
        return Lesson.LESSON;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.<Index>asList(Indexes.EDU_STUDENT_REFUND_IDX_COURSE_ID, Indexes.EDU_STUDENT_REFUND_IDX_STUDENT_ID);
    }

    @Override
    public Identity<EduStudentRefundRecord, Long> getIdentity() {
        return (Identity<EduStudentRefundRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<EduStudentRefundRecord> getPrimaryKey() {
        return Keys.KEY_EDU_STUDENT_REFUND_PRIMARY;
    }

    @Override
    public List<UniqueKey<EduStudentRefundRecord>> getKeys() {
        return Arrays.<UniqueKey<EduStudentRefundRecord>>asList(Keys.KEY_EDU_STUDENT_REFUND_PRIMARY);
    }

    @Override
    public EduStudentRefund as(String alias) {
        return new EduStudentRefund(DSL.name(alias), this);
    }

    @Override
    public EduStudentRefund as(Name alias) {
        return new EduStudentRefund(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public EduStudentRefund rename(String name) {
        return new EduStudentRefund(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public EduStudentRefund rename(Name name) {
        return new EduStudentRefund(name, null);
    }

    // -------------------------------------------------------------------------
    // Row14 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row14<Long, String, String, BigDecimal, BigDecimal, BigDecimal, BigDecimal, BigDecimal, String, Long, Long, LocalDateTime, LocalDateTime, Integer> fieldsRow() {
        return (Row14) super.fieldsRow();
    }
}
