/*
 * This file is generated by jOOQ.
 */
package com.lesson.repository.tables.records;


import com.lesson.repository.tables.EduCourseRecord;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record13;
import org.jooq.Row13;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 课程上课记录表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class EduCourseRecordRecord extends UpdatableRecordImpl<EduCourseRecordRecord> implements Record13<Long, Long, Long, String, LocalDateTime, LocalDateTime, BigDecimal, String, Long, Long, LocalDateTime, LocalDateTime, Integer> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lesson.edu_course_record.id</code>. 记录ID
     */
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>lesson.edu_course_record.id</code>. 记录ID
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>lesson.edu_course_record.course_id</code>. 课程ID
     */
    public void setCourseId(Long value) {
        set(1, value);
    }

    /**
     * Getter for <code>lesson.edu_course_record.course_id</code>. 课程ID
     */
    public Long getCourseId() {
        return (Long) get(1);
    }

    /**
     * Setter for <code>lesson.edu_course_record.coach_id</code>. 教练ID
     */
    public void setCoachId(Long value) {
        set(2, value);
    }

    /**
     * Getter for <code>lesson.edu_course_record.coach_id</code>. 教练ID
     */
    public Long getCoachId() {
        return (Long) get(2);
    }

    /**
     * Setter for <code>lesson.edu_course_record.coach_name</code>. 教练姓名
     */
    public void setCoachName(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>lesson.edu_course_record.coach_name</code>. 教练姓名
     */
    public String getCoachName() {
        return (String) get(3);
    }

    /**
     * Setter for <code>lesson.edu_course_record.start_time</code>. 上课开始时间
     */
    public void setStartTime(LocalDateTime value) {
        set(4, value);
    }

    /**
     * Getter for <code>lesson.edu_course_record.start_time</code>. 上课开始时间
     */
    public LocalDateTime getStartTime() {
        return (LocalDateTime) get(4);
    }

    /**
     * Setter for <code>lesson.edu_course_record.end_time</code>. 上课结束时间
     */
    public void setEndTime(LocalDateTime value) {
        set(5, value);
    }

    /**
     * Getter for <code>lesson.edu_course_record.end_time</code>. 上课结束时间
     */
    public LocalDateTime getEndTime() {
        return (LocalDateTime) get(5);
    }

    /**
     * Setter for <code>lesson.edu_course_record.hours</code>. 消耗课时数
     */
    public void setHours(BigDecimal value) {
        set(6, value);
    }

    /**
     * Getter for <code>lesson.edu_course_record.hours</code>. 消耗课时数
     */
    public BigDecimal getHours() {
        return (BigDecimal) get(6);
    }

    /**
     * Setter for <code>lesson.edu_course_record.note</code>. 课程记录备注
     */
    public void setNote(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>lesson.edu_course_record.note</code>. 课程记录备注
     */
    public String getNote() {
        return (String) get(7);
    }

    /**
     * Setter for <code>lesson.edu_course_record.campus_id</code>. 校区ID
     */
    public void setCampusId(Long value) {
        set(8, value);
    }

    /**
     * Getter for <code>lesson.edu_course_record.campus_id</code>. 校区ID
     */
    public Long getCampusId() {
        return (Long) get(8);
    }

    /**
     * Setter for <code>lesson.edu_course_record.institution_id</code>. 机构ID
     */
    public void setInstitutionId(Long value) {
        set(9, value);
    }

    /**
     * Getter for <code>lesson.edu_course_record.institution_id</code>. 机构ID
     */
    public Long getInstitutionId() {
        return (Long) get(9);
    }

    /**
     * Setter for <code>lesson.edu_course_record.created_time</code>. 创建时间
     */
    public void setCreatedTime(LocalDateTime value) {
        set(10, value);
    }

    /**
     * Getter for <code>lesson.edu_course_record.created_time</code>. 创建时间
     */
    public LocalDateTime getCreatedTime() {
        return (LocalDateTime) get(10);
    }

    /**
     * Setter for <code>lesson.edu_course_record.update_time</code>. 更新时间
     */
    public void setUpdateTime(LocalDateTime value) {
        set(11, value);
    }

    /**
     * Getter for <code>lesson.edu_course_record.update_time</code>. 更新时间
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(11);
    }

    /**
     * Setter for <code>lesson.edu_course_record.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public void setDeleted(Integer value) {
        set(12, value);
    }

    /**
     * Getter for <code>lesson.edu_course_record.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public Integer getDeleted() {
        return (Integer) get(12);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record13 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row13<Long, Long, Long, String, LocalDateTime, LocalDateTime, BigDecimal, String, Long, Long, LocalDateTime, LocalDateTime, Integer> fieldsRow() {
        return (Row13) super.fieldsRow();
    }

    @Override
    public Row13<Long, Long, Long, String, LocalDateTime, LocalDateTime, BigDecimal, String, Long, Long, LocalDateTime, LocalDateTime, Integer> valuesRow() {
        return (Row13) super.valuesRow();
    }

    @Override
    public Field<Long> field1() {
        return EduCourseRecord.EDU_COURSE_RECORD.ID;
    }

    @Override
    public Field<Long> field2() {
        return EduCourseRecord.EDU_COURSE_RECORD.COURSE_ID;
    }

    @Override
    public Field<Long> field3() {
        return EduCourseRecord.EDU_COURSE_RECORD.COACH_ID;
    }

    @Override
    public Field<String> field4() {
        return EduCourseRecord.EDU_COURSE_RECORD.COACH_NAME;
    }

    @Override
    public Field<LocalDateTime> field5() {
        return EduCourseRecord.EDU_COURSE_RECORD.START_TIME;
    }

    @Override
    public Field<LocalDateTime> field6() {
        return EduCourseRecord.EDU_COURSE_RECORD.END_TIME;
    }

    @Override
    public Field<BigDecimal> field7() {
        return EduCourseRecord.EDU_COURSE_RECORD.HOURS;
    }

    @Override
    public Field<String> field8() {
        return EduCourseRecord.EDU_COURSE_RECORD.NOTE;
    }

    @Override
    public Field<Long> field9() {
        return EduCourseRecord.EDU_COURSE_RECORD.CAMPUS_ID;
    }

    @Override
    public Field<Long> field10() {
        return EduCourseRecord.EDU_COURSE_RECORD.INSTITUTION_ID;
    }

    @Override
    public Field<LocalDateTime> field11() {
        return EduCourseRecord.EDU_COURSE_RECORD.CREATED_TIME;
    }

    @Override
    public Field<LocalDateTime> field12() {
        return EduCourseRecord.EDU_COURSE_RECORD.UPDATE_TIME;
    }

    @Override
    public Field<Integer> field13() {
        return EduCourseRecord.EDU_COURSE_RECORD.DELETED;
    }

    @Override
    public Long component1() {
        return getId();
    }

    @Override
    public Long component2() {
        return getCourseId();
    }

    @Override
    public Long component3() {
        return getCoachId();
    }

    @Override
    public String component4() {
        return getCoachName();
    }

    @Override
    public LocalDateTime component5() {
        return getStartTime();
    }

    @Override
    public LocalDateTime component6() {
        return getEndTime();
    }

    @Override
    public BigDecimal component7() {
        return getHours();
    }

    @Override
    public String component8() {
        return getNote();
    }

    @Override
    public Long component9() {
        return getCampusId();
    }

    @Override
    public Long component10() {
        return getInstitutionId();
    }

    @Override
    public LocalDateTime component11() {
        return getCreatedTime();
    }

    @Override
    public LocalDateTime component12() {
        return getUpdateTime();
    }

    @Override
    public Integer component13() {
        return getDeleted();
    }

    @Override
    public Long value1() {
        return getId();
    }

    @Override
    public Long value2() {
        return getCourseId();
    }

    @Override
    public Long value3() {
        return getCoachId();
    }

    @Override
    public String value4() {
        return getCoachName();
    }

    @Override
    public LocalDateTime value5() {
        return getStartTime();
    }

    @Override
    public LocalDateTime value6() {
        return getEndTime();
    }

    @Override
    public BigDecimal value7() {
        return getHours();
    }

    @Override
    public String value8() {
        return getNote();
    }

    @Override
    public Long value9() {
        return getCampusId();
    }

    @Override
    public Long value10() {
        return getInstitutionId();
    }

    @Override
    public LocalDateTime value11() {
        return getCreatedTime();
    }

    @Override
    public LocalDateTime value12() {
        return getUpdateTime();
    }

    @Override
    public Integer value13() {
        return getDeleted();
    }

    @Override
    public EduCourseRecordRecord value1(Long value) {
        setId(value);
        return this;
    }

    @Override
    public EduCourseRecordRecord value2(Long value) {
        setCourseId(value);
        return this;
    }

    @Override
    public EduCourseRecordRecord value3(Long value) {
        setCoachId(value);
        return this;
    }

    @Override
    public EduCourseRecordRecord value4(String value) {
        setCoachName(value);
        return this;
    }

    @Override
    public EduCourseRecordRecord value5(LocalDateTime value) {
        setStartTime(value);
        return this;
    }

    @Override
    public EduCourseRecordRecord value6(LocalDateTime value) {
        setEndTime(value);
        return this;
    }

    @Override
    public EduCourseRecordRecord value7(BigDecimal value) {
        setHours(value);
        return this;
    }

    @Override
    public EduCourseRecordRecord value8(String value) {
        setNote(value);
        return this;
    }

    @Override
    public EduCourseRecordRecord value9(Long value) {
        setCampusId(value);
        return this;
    }

    @Override
    public EduCourseRecordRecord value10(Long value) {
        setInstitutionId(value);
        return this;
    }

    @Override
    public EduCourseRecordRecord value11(LocalDateTime value) {
        setCreatedTime(value);
        return this;
    }

    @Override
    public EduCourseRecordRecord value12(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public EduCourseRecordRecord value13(Integer value) {
        setDeleted(value);
        return this;
    }

    @Override
    public EduCourseRecordRecord values(Long value1, Long value2, Long value3, String value4, LocalDateTime value5, LocalDateTime value6, BigDecimal value7, String value8, Long value9, Long value10, LocalDateTime value11, LocalDateTime value12, Integer value13) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached EduCourseRecordRecord
     */
    public EduCourseRecordRecord() {
        super(EduCourseRecord.EDU_COURSE_RECORD);
    }

    /**
     * Create a detached, initialised EduCourseRecordRecord
     */
    public EduCourseRecordRecord(Long id, Long courseId, Long coachId, String coachName, LocalDateTime startTime, LocalDateTime endTime, BigDecimal hours, String note, Long campusId, Long institutionId, LocalDateTime createdTime, LocalDateTime updateTime, Integer deleted) {
        super(EduCourseRecord.EDU_COURSE_RECORD);

        setId(id);
        setCourseId(courseId);
        setCoachId(coachId);
        setCoachName(coachName);
        setStartTime(startTime);
        setEndTime(endTime);
        setHours(hours);
        setNote(note);
        setCampusId(campusId);
        setInstitutionId(institutionId);
        setCreatedTime(createdTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
    }
}
