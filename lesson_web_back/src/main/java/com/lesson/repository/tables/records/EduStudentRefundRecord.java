/*
 * This file is generated by jOOQ.
 */
package com.lesson.repository.tables.records;


import com.lesson.repository.tables.EduStudentRefund;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record14;
import org.jooq.Row14;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 学员退费记录表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class EduStudentRefundRecord extends UpdatableRecordImpl<EduStudentRefundRecord> implements Record14<Long, String, String, BigDecimal, BigDecimal, BigDecimal, BigDecimal, BigDecimal, String, Long, Long, LocalDateTime, LocalDateTime, Integer> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lesson.edu_student_refund.id</code>. 记录ID
     */
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>lesson.edu_student_refund.id</code>. 记录ID
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>lesson.edu_student_refund.student_id</code>. 学员ID
     */
    public void setStudentId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>lesson.edu_student_refund.student_id</code>. 学员ID
     */
    public String getStudentId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>lesson.edu_student_refund.course_id</code>. 课程ID
     */
    public void setCourseId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>lesson.edu_student_refund.course_id</code>. 课程ID
     */
    public String getCourseId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>lesson.edu_student_refund.refund_hours</code>. 退课课时
     */
    public void setRefundHours(BigDecimal value) {
        set(3, value);
    }

    /**
     * Getter for <code>lesson.edu_student_refund.refund_hours</code>. 退课课时
     */
    public BigDecimal getRefundHours() {
        return (BigDecimal) get(3);
    }

    /**
     * Setter for <code>lesson.edu_student_refund.refund_amount</code>. 退款金额
     */
    public void setRefundAmount(BigDecimal value) {
        set(4, value);
    }

    /**
     * Getter for <code>lesson.edu_student_refund.refund_amount</code>. 退款金额
     */
    public BigDecimal getRefundAmount() {
        return (BigDecimal) get(4);
    }

    /**
     * Setter for <code>lesson.edu_student_refund.handling_fee</code>. 手续费
     */
    public void setHandlingFee(BigDecimal value) {
        set(5, value);
    }

    /**
     * Getter for <code>lesson.edu_student_refund.handling_fee</code>. 手续费
     */
    public BigDecimal getHandlingFee() {
        return (BigDecimal) get(5);
    }

    /**
     * Setter for <code>lesson.edu_student_refund.deduction_amount</code>. 其他费用扣除
     */
    public void setDeductionAmount(BigDecimal value) {
        set(6, value);
    }

    /**
     * Getter for <code>lesson.edu_student_refund.deduction_amount</code>. 其他费用扣除
     */
    public BigDecimal getDeductionAmount() {
        return (BigDecimal) get(6);
    }

    /**
     * Setter for <code>lesson.edu_student_refund.actual_refund</code>. 实际退款金额
     */
    public void setActualRefund(BigDecimal value) {
        set(7, value);
    }

    /**
     * Getter for <code>lesson.edu_student_refund.actual_refund</code>. 实际退款金额
     */
    public BigDecimal getActualRefund() {
        return (BigDecimal) get(7);
    }

    /**
     * Setter for <code>lesson.edu_student_refund.reason</code>. 退费原因
     */
    public void setReason(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>lesson.edu_student_refund.reason</code>. 退费原因
     */
    public String getReason() {
        return (String) get(8);
    }

    /**
     * Setter for <code>lesson.edu_student_refund.campus_id</code>. 校区ID
     */
    public void setCampusId(Long value) {
        set(9, value);
    }

    /**
     * Getter for <code>lesson.edu_student_refund.campus_id</code>. 校区ID
     */
    public Long getCampusId() {
        return (Long) get(9);
    }

    /**
     * Setter for <code>lesson.edu_student_refund.institution_id</code>. 机构ID
     */
    public void setInstitutionId(Long value) {
        set(10, value);
    }

    /**
     * Getter for <code>lesson.edu_student_refund.institution_id</code>. 机构ID
     */
    public Long getInstitutionId() {
        return (Long) get(10);
    }

    /**
     * Setter for <code>lesson.edu_student_refund.created_time</code>. 创建时间
     */
    public void setCreatedTime(LocalDateTime value) {
        set(11, value);
    }

    /**
     * Getter for <code>lesson.edu_student_refund.created_time</code>. 创建时间
     */
    public LocalDateTime getCreatedTime() {
        return (LocalDateTime) get(11);
    }

    /**
     * Setter for <code>lesson.edu_student_refund.update_time</code>. 更新时间
     */
    public void setUpdateTime(LocalDateTime value) {
        set(12, value);
    }

    /**
     * Getter for <code>lesson.edu_student_refund.update_time</code>. 更新时间
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(12);
    }

    /**
     * Setter for <code>lesson.edu_student_refund.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public void setDeleted(Integer value) {
        set(13, value);
    }

    /**
     * Getter for <code>lesson.edu_student_refund.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public Integer getDeleted() {
        return (Integer) get(13);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record14 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row14<Long, String, String, BigDecimal, BigDecimal, BigDecimal, BigDecimal, BigDecimal, String, Long, Long, LocalDateTime, LocalDateTime, Integer> fieldsRow() {
        return (Row14) super.fieldsRow();
    }

    @Override
    public Row14<Long, String, String, BigDecimal, BigDecimal, BigDecimal, BigDecimal, BigDecimal, String, Long, Long, LocalDateTime, LocalDateTime, Integer> valuesRow() {
        return (Row14) super.valuesRow();
    }

    @Override
    public Field<Long> field1() {
        return EduStudentRefund.EDU_STUDENT_REFUND.ID;
    }

    @Override
    public Field<String> field2() {
        return EduStudentRefund.EDU_STUDENT_REFUND.STUDENT_ID;
    }

    @Override
    public Field<String> field3() {
        return EduStudentRefund.EDU_STUDENT_REFUND.COURSE_ID;
    }

    @Override
    public Field<BigDecimal> field4() {
        return EduStudentRefund.EDU_STUDENT_REFUND.REFUND_HOURS;
    }

    @Override
    public Field<BigDecimal> field5() {
        return EduStudentRefund.EDU_STUDENT_REFUND.REFUND_AMOUNT;
    }

    @Override
    public Field<BigDecimal> field6() {
        return EduStudentRefund.EDU_STUDENT_REFUND.HANDLING_FEE;
    }

    @Override
    public Field<BigDecimal> field7() {
        return EduStudentRefund.EDU_STUDENT_REFUND.DEDUCTION_AMOUNT;
    }

    @Override
    public Field<BigDecimal> field8() {
        return EduStudentRefund.EDU_STUDENT_REFUND.ACTUAL_REFUND;
    }

    @Override
    public Field<String> field9() {
        return EduStudentRefund.EDU_STUDENT_REFUND.REASON;
    }

    @Override
    public Field<Long> field10() {
        return EduStudentRefund.EDU_STUDENT_REFUND.CAMPUS_ID;
    }

    @Override
    public Field<Long> field11() {
        return EduStudentRefund.EDU_STUDENT_REFUND.INSTITUTION_ID;
    }

    @Override
    public Field<LocalDateTime> field12() {
        return EduStudentRefund.EDU_STUDENT_REFUND.CREATED_TIME;
    }

    @Override
    public Field<LocalDateTime> field13() {
        return EduStudentRefund.EDU_STUDENT_REFUND.UPDATE_TIME;
    }

    @Override
    public Field<Integer> field14() {
        return EduStudentRefund.EDU_STUDENT_REFUND.DELETED;
    }

    @Override
    public Long component1() {
        return getId();
    }

    @Override
    public String component2() {
        return getStudentId();
    }

    @Override
    public String component3() {
        return getCourseId();
    }

    @Override
    public BigDecimal component4() {
        return getRefundHours();
    }

    @Override
    public BigDecimal component5() {
        return getRefundAmount();
    }

    @Override
    public BigDecimal component6() {
        return getHandlingFee();
    }

    @Override
    public BigDecimal component7() {
        return getDeductionAmount();
    }

    @Override
    public BigDecimal component8() {
        return getActualRefund();
    }

    @Override
    public String component9() {
        return getReason();
    }

    @Override
    public Long component10() {
        return getCampusId();
    }

    @Override
    public Long component11() {
        return getInstitutionId();
    }

    @Override
    public LocalDateTime component12() {
        return getCreatedTime();
    }

    @Override
    public LocalDateTime component13() {
        return getUpdateTime();
    }

    @Override
    public Integer component14() {
        return getDeleted();
    }

    @Override
    public Long value1() {
        return getId();
    }

    @Override
    public String value2() {
        return getStudentId();
    }

    @Override
    public String value3() {
        return getCourseId();
    }

    @Override
    public BigDecimal value4() {
        return getRefundHours();
    }

    @Override
    public BigDecimal value5() {
        return getRefundAmount();
    }

    @Override
    public BigDecimal value6() {
        return getHandlingFee();
    }

    @Override
    public BigDecimal value7() {
        return getDeductionAmount();
    }

    @Override
    public BigDecimal value8() {
        return getActualRefund();
    }

    @Override
    public String value9() {
        return getReason();
    }

    @Override
    public Long value10() {
        return getCampusId();
    }

    @Override
    public Long value11() {
        return getInstitutionId();
    }

    @Override
    public LocalDateTime value12() {
        return getCreatedTime();
    }

    @Override
    public LocalDateTime value13() {
        return getUpdateTime();
    }

    @Override
    public Integer value14() {
        return getDeleted();
    }

    @Override
    public EduStudentRefundRecord value1(Long value) {
        setId(value);
        return this;
    }

    @Override
    public EduStudentRefundRecord value2(String value) {
        setStudentId(value);
        return this;
    }

    @Override
    public EduStudentRefundRecord value3(String value) {
        setCourseId(value);
        return this;
    }

    @Override
    public EduStudentRefundRecord value4(BigDecimal value) {
        setRefundHours(value);
        return this;
    }

    @Override
    public EduStudentRefundRecord value5(BigDecimal value) {
        setRefundAmount(value);
        return this;
    }

    @Override
    public EduStudentRefundRecord value6(BigDecimal value) {
        setHandlingFee(value);
        return this;
    }

    @Override
    public EduStudentRefundRecord value7(BigDecimal value) {
        setDeductionAmount(value);
        return this;
    }

    @Override
    public EduStudentRefundRecord value8(BigDecimal value) {
        setActualRefund(value);
        return this;
    }

    @Override
    public EduStudentRefundRecord value9(String value) {
        setReason(value);
        return this;
    }

    @Override
    public EduStudentRefundRecord value10(Long value) {
        setCampusId(value);
        return this;
    }

    @Override
    public EduStudentRefundRecord value11(Long value) {
        setInstitutionId(value);
        return this;
    }

    @Override
    public EduStudentRefundRecord value12(LocalDateTime value) {
        setCreatedTime(value);
        return this;
    }

    @Override
    public EduStudentRefundRecord value13(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public EduStudentRefundRecord value14(Integer value) {
        setDeleted(value);
        return this;
    }

    @Override
    public EduStudentRefundRecord values(Long value1, String value2, String value3, BigDecimal value4, BigDecimal value5, BigDecimal value6, BigDecimal value7, BigDecimal value8, String value9, Long value10, Long value11, LocalDateTime value12, LocalDateTime value13, Integer value14) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached EduStudentRefundRecord
     */
    public EduStudentRefundRecord() {
        super(EduStudentRefund.EDU_STUDENT_REFUND);
    }

    /**
     * Create a detached, initialised EduStudentRefundRecord
     */
    public EduStudentRefundRecord(Long id, String studentId, String courseId, BigDecimal refundHours, BigDecimal refundAmount, BigDecimal handlingFee, BigDecimal deductionAmount, BigDecimal actualRefund, String reason, Long campusId, Long institutionId, LocalDateTime createdTime, LocalDateTime updateTime, Integer deleted) {
        super(EduStudentRefund.EDU_STUDENT_REFUND);

        setId(id);
        setStudentId(studentId);
        setCourseId(courseId);
        setRefundHours(refundHours);
        setRefundAmount(refundAmount);
        setHandlingFee(handlingFee);
        setDeductionAmount(deductionAmount);
        setActualRefund(actualRefund);
        setReason(reason);
        setCampusId(campusId);
        setInstitutionId(institutionId);
        setCreatedTime(createdTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
    }
}
