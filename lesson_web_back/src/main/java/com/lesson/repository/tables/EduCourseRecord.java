/*
 * This file is generated by jOOQ.
 */
package com.lesson.repository.tables;


import com.lesson.repository.Indexes;
import com.lesson.repository.Keys;
import com.lesson.repository.Lesson;
import com.lesson.repository.tables.records.EduCourseRecordRecord;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Identity;
import org.jooq.Index;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row13;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 课程上课记录表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class EduCourseRecord extends TableImpl<EduCourseRecordRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>lesson.edu_course_record</code>
     */
    public static final EduCourseRecord EDU_COURSE_RECORD = new EduCourseRecord();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<EduCourseRecordRecord> getRecordType() {
        return EduCourseRecordRecord.class;
    }

    /**
     * The column <code>lesson.edu_course_record.id</code>. 记录ID
     */
    public final TableField<EduCourseRecordRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "记录ID");

    /**
     * The column <code>lesson.edu_course_record.course_id</code>. 课程ID
     */
    public final TableField<EduCourseRecordRecord, Long> COURSE_ID = createField(DSL.name("course_id"), SQLDataType.BIGINT.nullable(false), this, "课程ID");

    /**
     * The column <code>lesson.edu_course_record.coach_id</code>. 教练ID
     */
    public final TableField<EduCourseRecordRecord, Long> COACH_ID = createField(DSL.name("coach_id"), SQLDataType.BIGINT.nullable(false), this, "教练ID");

    /**
     * The column <code>lesson.edu_course_record.coach_name</code>. 教练姓名
     */
    public final TableField<EduCourseRecordRecord, String> COACH_NAME = createField(DSL.name("coach_name"), SQLDataType.VARCHAR(50).nullable(false), this, "教练姓名");

    /**
     * The column <code>lesson.edu_course_record.start_time</code>. 上课开始时间
     */
    public final TableField<EduCourseRecordRecord, LocalDateTime> START_TIME = createField(DSL.name("start_time"), SQLDataType.LOCALDATETIME(0).nullable(false), this, "上课开始时间");

    /**
     * The column <code>lesson.edu_course_record.end_time</code>. 上课结束时间
     */
    public final TableField<EduCourseRecordRecord, LocalDateTime> END_TIME = createField(DSL.name("end_time"), SQLDataType.LOCALDATETIME(0).nullable(false), this, "上课结束时间");

    /**
     * The column <code>lesson.edu_course_record.hours</code>. 消耗课时数
     */
    public final TableField<EduCourseRecordRecord, BigDecimal> HOURS = createField(DSL.name("hours"), SQLDataType.DECIMAL(10, 2).nullable(false), this, "消耗课时数");

    /**
     * The column <code>lesson.edu_course_record.note</code>. 课程记录备注
     */
    public final TableField<EduCourseRecordRecord, String> NOTE = createField(DSL.name("note"), SQLDataType.VARCHAR(500), this, "课程记录备注");

    /**
     * The column <code>lesson.edu_course_record.campus_id</code>. 校区ID
     */
    public final TableField<EduCourseRecordRecord, Long> CAMPUS_ID = createField(DSL.name("campus_id"), SQLDataType.BIGINT.nullable(false), this, "校区ID");

    /**
     * The column <code>lesson.edu_course_record.institution_id</code>. 机构ID
     */
    public final TableField<EduCourseRecordRecord, Long> INSTITUTION_ID = createField(DSL.name("institution_id"), SQLDataType.BIGINT.nullable(false), this, "机构ID");

    /**
     * The column <code>lesson.edu_course_record.created_time</code>. 创建时间
     */
    public final TableField<EduCourseRecordRecord, LocalDateTime> CREATED_TIME = createField(DSL.name("created_time"), SQLDataType.LOCALDATETIME(0).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "创建时间");

    /**
     * The column <code>lesson.edu_course_record.update_time</code>. 更新时间
     */
    public final TableField<EduCourseRecordRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(0).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "更新时间");

    /**
     * The column <code>lesson.edu_course_record.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public final TableField<EduCourseRecordRecord, Integer> DELETED = createField(DSL.name("deleted"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否删除：0-未删除，1-已删除");

    private EduCourseRecord(Name alias, Table<EduCourseRecordRecord> aliased) {
        this(alias, aliased, null);
    }

    private EduCourseRecord(Name alias, Table<EduCourseRecordRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("课程上课记录表"), TableOptions.table());
    }

    /**
     * Create an aliased <code>lesson.edu_course_record</code> table reference
     */
    public EduCourseRecord(String alias) {
        this(DSL.name(alias), EDU_COURSE_RECORD);
    }

    /**
     * Create an aliased <code>lesson.edu_course_record</code> table reference
     */
    public EduCourseRecord(Name alias) {
        this(alias, EDU_COURSE_RECORD);
    }

    /**
     * Create a <code>lesson.edu_course_record</code> table reference
     */
    public EduCourseRecord() {
        this(DSL.name("edu_course_record"), null);
    }

    public <O extends Record> EduCourseRecord(Table<O> child, ForeignKey<O, EduCourseRecordRecord> key) {
        super(child, key, EDU_COURSE_RECORD);
    }

    @Override
    public Schema getSchema() {
        return Lesson.LESSON;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.<Index>asList(Indexes.EDU_COURSE_RECORD_IDX_CAMPUS_ID, Indexes.EDU_COURSE_RECORD_IDX_COACH_ID, Indexes.EDU_COURSE_RECORD_IDX_COURSE_ID, Indexes.EDU_COURSE_RECORD_IDX_INSTITUTION_ID, Indexes.EDU_COURSE_RECORD_IDX_START_TIME);
    }

    @Override
    public Identity<EduCourseRecordRecord, Long> getIdentity() {
        return (Identity<EduCourseRecordRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<EduCourseRecordRecord> getPrimaryKey() {
        return Keys.KEY_EDU_COURSE_RECORD_PRIMARY;
    }

    @Override
    public List<UniqueKey<EduCourseRecordRecord>> getKeys() {
        return Arrays.<UniqueKey<EduCourseRecordRecord>>asList(Keys.KEY_EDU_COURSE_RECORD_PRIMARY);
    }

    @Override
    public EduCourseRecord as(String alias) {
        return new EduCourseRecord(DSL.name(alias), this);
    }

    @Override
    public EduCourseRecord as(Name alias) {
        return new EduCourseRecord(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public EduCourseRecord rename(String name) {
        return new EduCourseRecord(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public EduCourseRecord rename(Name name) {
        return new EduCourseRecord(name, null);
    }

    // -------------------------------------------------------------------------
    // Row13 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row13<Long, Long, Long, String, LocalDateTime, LocalDateTime, BigDecimal, String, Long, Long, LocalDateTime, LocalDateTime, Integer> fieldsRow() {
        return (Row13) super.fieldsRow();
    }
}
