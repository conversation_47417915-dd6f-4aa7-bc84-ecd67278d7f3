/*
 * This file is generated by jOOQ.
 */
package com.lesson.repository.tables.records;


import com.lesson.repository.tables.SysCoach;

import java.time.LocalDate;
import java.time.LocalDateTime;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record15;
import org.jooq.Row15;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 教练表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SysCoachRecord extends UpdatableRecordImpl<SysCoachRecord> implements Record15<Long, String, String, Integer, String, String, String, LocalDate, Integer, String, Long, Long, LocalDateTime, LocalDateTime, Integer> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lesson.sys_coach.id</code>. 教练ID
     */
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>lesson.sys_coach.id</code>. 教练ID
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>lesson.sys_coach.name</code>. 姓名
     */
    public void setName(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>lesson.sys_coach.name</code>. 姓名
     */
    public String getName() {
        return (String) get(1);
    }

    /**
     * Setter for <code>lesson.sys_coach.gender</code>. 性别
     */
    public void setGender(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>lesson.sys_coach.gender</code>. 性别
     */
    public String getGender() {
        return (String) get(2);
    }

    /**
     * Setter for <code>lesson.sys_coach.age</code>. 年龄
     */
    public void setAge(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>lesson.sys_coach.age</code>. 年龄
     */
    public Integer getAge() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>lesson.sys_coach.phone</code>. 联系电话
     */
    public void setPhone(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>lesson.sys_coach.phone</code>. 联系电话
     */
    public String getPhone() {
        return (String) get(4);
    }

    /**
     * Setter for <code>lesson.sys_coach.avatar</code>. 头像URL
     */
    public void setAvatar(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>lesson.sys_coach.avatar</code>. 头像URL
     */
    public String getAvatar() {
        return (String) get(5);
    }

    /**
     * Setter for <code>lesson.sys_coach.job_title</code>. 职位
     */
    public void setJobTitle(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>lesson.sys_coach.job_title</code>. 职位
     */
    public String getJobTitle() {
        return (String) get(6);
    }

    /**
     * Setter for <code>lesson.sys_coach.hire_date</code>. 入职日期
     */
    public void setHireDate(LocalDate value) {
        set(7, value);
    }

    /**
     * Getter for <code>lesson.sys_coach.hire_date</code>. 入职日期
     */
    public LocalDate getHireDate() {
        return (LocalDate) get(7);
    }

    /**
     * Setter for <code>lesson.sys_coach.experience</code>. 教龄(年)
     */
    public void setExperience(Integer value) {
        set(8, value);
    }

    /**
     * Getter for <code>lesson.sys_coach.experience</code>. 教龄(年)
     */
    public Integer getExperience() {
        return (Integer) get(8);
    }

    /**
     * Setter for <code>lesson.sys_coach.status</code>. 状态：在职/休假中/离职
     */
    public void setStatus(String value) {
        set(9, value);
    }

    /**
     * Getter for <code>lesson.sys_coach.status</code>. 状态：在职/休假中/离职
     */
    public String getStatus() {
        return (String) get(9);
    }

    /**
     * Setter for <code>lesson.sys_coach.campus_id</code>. 所属校区ID
     */
    public void setCampusId(Long value) {
        set(10, value);
    }

    /**
     * Getter for <code>lesson.sys_coach.campus_id</code>. 所属校区ID
     */
    public Long getCampusId() {
        return (Long) get(10);
    }

    /**
     * Setter for <code>lesson.sys_coach.institution_id</code>. 所属机构ID
     */
    public void setInstitutionId(Long value) {
        set(11, value);
    }

    /**
     * Getter for <code>lesson.sys_coach.institution_id</code>. 所属机构ID
     */
    public Long getInstitutionId() {
        return (Long) get(11);
    }

    /**
     * Setter for <code>lesson.sys_coach.created_time</code>. 创建时间
     */
    public void setCreatedTime(LocalDateTime value) {
        set(12, value);
    }

    /**
     * Getter for <code>lesson.sys_coach.created_time</code>. 创建时间
     */
    public LocalDateTime getCreatedTime() {
        return (LocalDateTime) get(12);
    }

    /**
     * Setter for <code>lesson.sys_coach.update_time</code>. 更新时间
     */
    public void setUpdateTime(LocalDateTime value) {
        set(13, value);
    }

    /**
     * Getter for <code>lesson.sys_coach.update_time</code>. 更新时间
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(13);
    }

    /**
     * Setter for <code>lesson.sys_coach.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public void setDeleted(Integer value) {
        set(14, value);
    }

    /**
     * Getter for <code>lesson.sys_coach.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public Integer getDeleted() {
        return (Integer) get(14);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record15 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row15<Long, String, String, Integer, String, String, String, LocalDate, Integer, String, Long, Long, LocalDateTime, LocalDateTime, Integer> fieldsRow() {
        return (Row15) super.fieldsRow();
    }

    @Override
    public Row15<Long, String, String, Integer, String, String, String, LocalDate, Integer, String, Long, Long, LocalDateTime, LocalDateTime, Integer> valuesRow() {
        return (Row15) super.valuesRow();
    }

    @Override
    public Field<Long> field1() {
        return SysCoach.SYS_COACH.ID;
    }

    @Override
    public Field<String> field2() {
        return SysCoach.SYS_COACH.NAME;
    }

    @Override
    public Field<String> field3() {
        return SysCoach.SYS_COACH.GENDER;
    }

    @Override
    public Field<Integer> field4() {
        return SysCoach.SYS_COACH.AGE;
    }

    @Override
    public Field<String> field5() {
        return SysCoach.SYS_COACH.PHONE;
    }

    @Override
    public Field<String> field6() {
        return SysCoach.SYS_COACH.AVATAR;
    }

    @Override
    public Field<String> field7() {
        return SysCoach.SYS_COACH.JOB_TITLE;
    }

    @Override
    public Field<LocalDate> field8() {
        return SysCoach.SYS_COACH.HIRE_DATE;
    }

    @Override
    public Field<Integer> field9() {
        return SysCoach.SYS_COACH.EXPERIENCE;
    }

    @Override
    public Field<String> field10() {
        return SysCoach.SYS_COACH.STATUS;
    }

    @Override
    public Field<Long> field11() {
        return SysCoach.SYS_COACH.CAMPUS_ID;
    }

    @Override
    public Field<Long> field12() {
        return SysCoach.SYS_COACH.INSTITUTION_ID;
    }

    @Override
    public Field<LocalDateTime> field13() {
        return SysCoach.SYS_COACH.CREATED_TIME;
    }

    @Override
    public Field<LocalDateTime> field14() {
        return SysCoach.SYS_COACH.UPDATE_TIME;
    }

    @Override
    public Field<Integer> field15() {
        return SysCoach.SYS_COACH.DELETED;
    }

    @Override
    public Long component1() {
        return getId();
    }

    @Override
    public String component2() {
        return getName();
    }

    @Override
    public String component3() {
        return getGender();
    }

    @Override
    public Integer component4() {
        return getAge();
    }

    @Override
    public String component5() {
        return getPhone();
    }

    @Override
    public String component6() {
        return getAvatar();
    }

    @Override
    public String component7() {
        return getJobTitle();
    }

    @Override
    public LocalDate component8() {
        return getHireDate();
    }

    @Override
    public Integer component9() {
        return getExperience();
    }

    @Override
    public String component10() {
        return getStatus();
    }

    @Override
    public Long component11() {
        return getCampusId();
    }

    @Override
    public Long component12() {
        return getInstitutionId();
    }

    @Override
    public LocalDateTime component13() {
        return getCreatedTime();
    }

    @Override
    public LocalDateTime component14() {
        return getUpdateTime();
    }

    @Override
    public Integer component15() {
        return getDeleted();
    }

    @Override
    public Long value1() {
        return getId();
    }

    @Override
    public String value2() {
        return getName();
    }

    @Override
    public String value3() {
        return getGender();
    }

    @Override
    public Integer value4() {
        return getAge();
    }

    @Override
    public String value5() {
        return getPhone();
    }

    @Override
    public String value6() {
        return getAvatar();
    }

    @Override
    public String value7() {
        return getJobTitle();
    }

    @Override
    public LocalDate value8() {
        return getHireDate();
    }

    @Override
    public Integer value9() {
        return getExperience();
    }

    @Override
    public String value10() {
        return getStatus();
    }

    @Override
    public Long value11() {
        return getCampusId();
    }

    @Override
    public Long value12() {
        return getInstitutionId();
    }

    @Override
    public LocalDateTime value13() {
        return getCreatedTime();
    }

    @Override
    public LocalDateTime value14() {
        return getUpdateTime();
    }

    @Override
    public Integer value15() {
        return getDeleted();
    }

    @Override
    public SysCoachRecord value1(Long value) {
        setId(value);
        return this;
    }

    @Override
    public SysCoachRecord value2(String value) {
        setName(value);
        return this;
    }

    @Override
    public SysCoachRecord value3(String value) {
        setGender(value);
        return this;
    }

    @Override
    public SysCoachRecord value4(Integer value) {
        setAge(value);
        return this;
    }

    @Override
    public SysCoachRecord value5(String value) {
        setPhone(value);
        return this;
    }

    @Override
    public SysCoachRecord value6(String value) {
        setAvatar(value);
        return this;
    }

    @Override
    public SysCoachRecord value7(String value) {
        setJobTitle(value);
        return this;
    }

    @Override
    public SysCoachRecord value8(LocalDate value) {
        setHireDate(value);
        return this;
    }

    @Override
    public SysCoachRecord value9(Integer value) {
        setExperience(value);
        return this;
    }

    @Override
    public SysCoachRecord value10(String value) {
        setStatus(value);
        return this;
    }

    @Override
    public SysCoachRecord value11(Long value) {
        setCampusId(value);
        return this;
    }

    @Override
    public SysCoachRecord value12(Long value) {
        setInstitutionId(value);
        return this;
    }

    @Override
    public SysCoachRecord value13(LocalDateTime value) {
        setCreatedTime(value);
        return this;
    }

    @Override
    public SysCoachRecord value14(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public SysCoachRecord value15(Integer value) {
        setDeleted(value);
        return this;
    }

    @Override
    public SysCoachRecord values(Long value1, String value2, String value3, Integer value4, String value5, String value6, String value7, LocalDate value8, Integer value9, String value10, Long value11, Long value12, LocalDateTime value13, LocalDateTime value14, Integer value15) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        value15(value15);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached SysCoachRecord
     */
    public SysCoachRecord() {
        super(SysCoach.SYS_COACH);
    }

    /**
     * Create a detached, initialised SysCoachRecord
     */
    public SysCoachRecord(Long id, String name, String gender, Integer age, String phone, String avatar, String jobTitle, LocalDate hireDate, Integer experience, String status, Long campusId, Long institutionId, LocalDateTime createdTime, LocalDateTime updateTime, Integer deleted) {
        super(SysCoach.SYS_COACH);

        setId(id);
        setName(name);
        setGender(gender);
        setAge(age);
        setPhone(phone);
        setAvatar(avatar);
        setJobTitle(jobTitle);
        setHireDate(hireDate);
        setExperience(experience);
        setStatus(status);
        setCampusId(campusId);
        setInstitutionId(institutionId);
        setCreatedTime(createdTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
    }
}
