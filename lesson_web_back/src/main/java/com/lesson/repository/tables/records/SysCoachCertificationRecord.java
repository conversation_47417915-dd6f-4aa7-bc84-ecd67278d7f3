/*
 * This file is generated by jOOQ.
 */
package com.lesson.repository.tables.records;


import com.lesson.repository.tables.SysCoachCertification;

import java.time.LocalDateTime;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record6;
import org.jooq.Row6;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 教练证书表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SysCoachCertificationRecord extends UpdatableRecordImpl<SysCoachCertificationRecord> implements Record6<Long, Long, String, LocalDateTime, LocalDateTime, Integer> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lesson.sys_coach_certification.id</code>. 主键ID
     */
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>lesson.sys_coach_certification.id</code>. 主键ID
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>lesson.sys_coach_certification.coach_id</code>. 关联教练ID
     */
    public void setCoachId(Long value) {
        set(1, value);
    }

    /**
     * Getter for <code>lesson.sys_coach_certification.coach_id</code>. 关联教练ID
     */
    public Long getCoachId() {
        return (Long) get(1);
    }

    /**
     * Setter for <code>lesson.sys_coach_certification.certification_name</code>. 证书名称
     */
    public void setCertificationName(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>lesson.sys_coach_certification.certification_name</code>. 证书名称
     */
    public String getCertificationName() {
        return (String) get(2);
    }

    /**
     * Setter for <code>lesson.sys_coach_certification.created_time</code>. 创建时间
     */
    public void setCreatedTime(LocalDateTime value) {
        set(3, value);
    }

    /**
     * Getter for <code>lesson.sys_coach_certification.created_time</code>. 创建时间
     */
    public LocalDateTime getCreatedTime() {
        return (LocalDateTime) get(3);
    }

    /**
     * Setter for <code>lesson.sys_coach_certification.update_time</code>. 更新时间
     */
    public void setUpdateTime(LocalDateTime value) {
        set(4, value);
    }

    /**
     * Getter for <code>lesson.sys_coach_certification.update_time</code>. 更新时间
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(4);
    }

    /**
     * Setter for <code>lesson.sys_coach_certification.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public void setDeleted(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>lesson.sys_coach_certification.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public Integer getDeleted() {
        return (Integer) get(5);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record6 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row6<Long, Long, String, LocalDateTime, LocalDateTime, Integer> fieldsRow() {
        return (Row6) super.fieldsRow();
    }

    @Override
    public Row6<Long, Long, String, LocalDateTime, LocalDateTime, Integer> valuesRow() {
        return (Row6) super.valuesRow();
    }

    @Override
    public Field<Long> field1() {
        return SysCoachCertification.SYS_COACH_CERTIFICATION.ID;
    }

    @Override
    public Field<Long> field2() {
        return SysCoachCertification.SYS_COACH_CERTIFICATION.COACH_ID;
    }

    @Override
    public Field<String> field3() {
        return SysCoachCertification.SYS_COACH_CERTIFICATION.CERTIFICATION_NAME;
    }

    @Override
    public Field<LocalDateTime> field4() {
        return SysCoachCertification.SYS_COACH_CERTIFICATION.CREATED_TIME;
    }

    @Override
    public Field<LocalDateTime> field5() {
        return SysCoachCertification.SYS_COACH_CERTIFICATION.UPDATE_TIME;
    }

    @Override
    public Field<Integer> field6() {
        return SysCoachCertification.SYS_COACH_CERTIFICATION.DELETED;
    }

    @Override
    public Long component1() {
        return getId();
    }

    @Override
    public Long component2() {
        return getCoachId();
    }

    @Override
    public String component3() {
        return getCertificationName();
    }

    @Override
    public LocalDateTime component4() {
        return getCreatedTime();
    }

    @Override
    public LocalDateTime component5() {
        return getUpdateTime();
    }

    @Override
    public Integer component6() {
        return getDeleted();
    }

    @Override
    public Long value1() {
        return getId();
    }

    @Override
    public Long value2() {
        return getCoachId();
    }

    @Override
    public String value3() {
        return getCertificationName();
    }

    @Override
    public LocalDateTime value4() {
        return getCreatedTime();
    }

    @Override
    public LocalDateTime value5() {
        return getUpdateTime();
    }

    @Override
    public Integer value6() {
        return getDeleted();
    }

    @Override
    public SysCoachCertificationRecord value1(Long value) {
        setId(value);
        return this;
    }

    @Override
    public SysCoachCertificationRecord value2(Long value) {
        setCoachId(value);
        return this;
    }

    @Override
    public SysCoachCertificationRecord value3(String value) {
        setCertificationName(value);
        return this;
    }

    @Override
    public SysCoachCertificationRecord value4(LocalDateTime value) {
        setCreatedTime(value);
        return this;
    }

    @Override
    public SysCoachCertificationRecord value5(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public SysCoachCertificationRecord value6(Integer value) {
        setDeleted(value);
        return this;
    }

    @Override
    public SysCoachCertificationRecord values(Long value1, Long value2, String value3, LocalDateTime value4, LocalDateTime value5, Integer value6) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached SysCoachCertificationRecord
     */
    public SysCoachCertificationRecord() {
        super(SysCoachCertification.SYS_COACH_CERTIFICATION);
    }

    /**
     * Create a detached, initialised SysCoachCertificationRecord
     */
    public SysCoachCertificationRecord(Long id, Long coachId, String certificationName, LocalDateTime createdTime, LocalDateTime updateTime, Integer deleted) {
        super(SysCoachCertification.SYS_COACH_CERTIFICATION);

        setId(id);
        setCoachId(coachId);
        setCertificationName(certificationName);
        setCreatedTime(createdTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
    }
}
