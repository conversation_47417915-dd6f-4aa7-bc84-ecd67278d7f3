/*
 * This file is generated by jOOQ.
 */
package com.lesson.repository.tables;


import com.lesson.repository.Indexes;
import com.lesson.repository.Keys;
import com.lesson.repository.Lesson;
import com.lesson.repository.tables.records.EduStudentCourseOperationRecord;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Identity;
import org.jooq.Index;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row22;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 学员课程操作记录表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class EduStudentCourseOperation extends TableImpl<EduStudentCourseOperationRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>lesson.edu_student_course_operation</code>
     */
    public static final EduStudentCourseOperation EDU_STUDENT_COURSE_OPERATION = new EduStudentCourseOperation();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<EduStudentCourseOperationRecord> getRecordType() {
        return EduStudentCourseOperationRecord.class;
    }

    /**
     * The column <code>lesson.edu_student_course_operation.id</code>. 记录ID
     */
    public final TableField<EduStudentCourseOperationRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "记录ID");

    /**
     * The column <code>lesson.edu_student_course_operation.student_id</code>. 学员ID
     */
    public final TableField<EduStudentCourseOperationRecord, Long> STUDENT_ID = createField(DSL.name("student_id"), SQLDataType.BIGINT.nullable(false), this, "学员ID");

    /**
     * The column <code>lesson.edu_student_course_operation.student_name</code>. 学员姓名
     */
    public final TableField<EduStudentCourseOperationRecord, String> STUDENT_NAME = createField(DSL.name("student_name"), SQLDataType.VARCHAR(50).nullable(false), this, "学员姓名");

    /**
     * The column <code>lesson.edu_student_course_operation.course_id</code>. 课程ID
     */
    public final TableField<EduStudentCourseOperationRecord, Long> COURSE_ID = createField(DSL.name("course_id"), SQLDataType.BIGINT.nullable(false), this, "课程ID");

    /**
     * The column <code>lesson.edu_student_course_operation.operation_type</code>. 操作类型：TRANSFER_COURSE-转课，TRANSFER_CLASS-转班，REFUND-退费
     */
    public final TableField<EduStudentCourseOperationRecord, String> OPERATION_TYPE = createField(DSL.name("operation_type"), SQLDataType.VARCHAR(20).nullable(false), this, "操作类型：TRANSFER_COURSE-转课，TRANSFER_CLASS-转班，REFUND-退费");

    /**
     * The column <code>lesson.edu_student_course_operation.before_status</code>. 操作前状态
     */
    public final TableField<EduStudentCourseOperationRecord, String> BEFORE_STATUS = createField(DSL.name("before_status"), SQLDataType.VARCHAR(20).nullable(false), this, "操作前状态");

    /**
     * The column <code>lesson.edu_student_course_operation.after_status</code>. 操作后状态
     */
    public final TableField<EduStudentCourseOperationRecord, String> AFTER_STATUS = createField(DSL.name("after_status"), SQLDataType.VARCHAR(20).nullable(false), this, "操作后状态");

    /**
     * The column <code>lesson.edu_student_course_operation.source_course_id</code>. 原课程ID
     */
    public final TableField<EduStudentCourseOperationRecord, Long> SOURCE_COURSE_ID = createField(DSL.name("source_course_id"), SQLDataType.BIGINT, this, "原课程ID");

    /**
     * The column <code>lesson.edu_student_course_operation.target_course_id</code>. 目标课程ID
     */
    public final TableField<EduStudentCourseOperationRecord, Long> TARGET_COURSE_ID = createField(DSL.name("target_course_id"), SQLDataType.BIGINT, this, "目标课程ID");

    /**
     * The column <code>lesson.edu_student_course_operation.source_class_id</code>. 原班级ID
     */
    public final TableField<EduStudentCourseOperationRecord, Long> SOURCE_CLASS_ID = createField(DSL.name("source_class_id"), SQLDataType.BIGINT, this, "原班级ID");

    /**
     * The column <code>lesson.edu_student_course_operation.source_class_name</code>. 原班级名称
     */
    public final TableField<EduStudentCourseOperationRecord, String> SOURCE_CLASS_NAME = createField(DSL.name("source_class_name"), SQLDataType.VARCHAR(100), this, "原班级名称");

    /**
     * The column <code>lesson.edu_student_course_operation.target_class_id</code>. 目标班级ID
     */
    public final TableField<EduStudentCourseOperationRecord, Long> TARGET_CLASS_ID = createField(DSL.name("target_class_id"), SQLDataType.BIGINT, this, "目标班级ID");

    /**
     * The column <code>lesson.edu_student_course_operation.target_class_name</code>. 目标班级名称
     */
    public final TableField<EduStudentCourseOperationRecord, String> TARGET_CLASS_NAME = createField(DSL.name("target_class_name"), SQLDataType.VARCHAR(100), this, "目标班级名称");

    /**
     * The column <code>lesson.edu_student_course_operation.refund_amount</code>. 退费金额
     */
    public final TableField<EduStudentCourseOperationRecord, BigDecimal> REFUND_AMOUNT = createField(DSL.name("refund_amount"), SQLDataType.DECIMAL(10, 2), this, "退费金额");

    /**
     * The column <code>lesson.edu_student_course_operation.refund_method</code>. 退费方式：CASH-现金，BANK_TRANSFER-银行转账，WECHAT-微信，ALIPAY-支付宝
     */
    public final TableField<EduStudentCourseOperationRecord, String> REFUND_METHOD = createField(DSL.name("refund_method"), SQLDataType.VARCHAR(20), this, "退费方式：CASH-现金，BANK_TRANSFER-银行转账，WECHAT-微信，ALIPAY-支付宝");

    /**
     * The column <code>lesson.edu_student_course_operation.operation_reason</code>. 操作原因
     */
    public final TableField<EduStudentCourseOperationRecord, String> OPERATION_REASON = createField(DSL.name("operation_reason"), SQLDataType.VARCHAR(500), this, "操作原因");

    /**
     * The column <code>lesson.edu_student_course_operation.operator_id</code>. 操作人ID
     */
    public final TableField<EduStudentCourseOperationRecord, Long> OPERATOR_ID = createField(DSL.name("operator_id"), SQLDataType.BIGINT.nullable(false), this, "操作人ID");

    /**
     * The column <code>lesson.edu_student_course_operation.operator_name</code>. 操作人姓名
     */
    public final TableField<EduStudentCourseOperationRecord, String> OPERATOR_NAME = createField(DSL.name("operator_name"), SQLDataType.VARCHAR(50).nullable(false), this, "操作人姓名");

    /**
     * The column <code>lesson.edu_student_course_operation.operation_time</code>. 操作时间
     */
    public final TableField<EduStudentCourseOperationRecord, LocalDateTime> OPERATION_TIME = createField(DSL.name("operation_time"), SQLDataType.LOCALDATETIME(0).nullable(false), this, "操作时间");

    /**
     * The column <code>lesson.edu_student_course_operation.created_time</code>. 创建时间
     */
    public final TableField<EduStudentCourseOperationRecord, LocalDateTime> CREATED_TIME = createField(DSL.name("created_time"), SQLDataType.LOCALDATETIME(0).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "创建时间");

    /**
     * The column <code>lesson.edu_student_course_operation.update_time</code>. 更新时间
     */
    public final TableField<EduStudentCourseOperationRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(0).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "更新时间");

    /**
     * The column <code>lesson.edu_student_course_operation.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public final TableField<EduStudentCourseOperationRecord, Integer> DELETED = createField(DSL.name("deleted"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否删除：0-未删除，1-已删除");

    private EduStudentCourseOperation(Name alias, Table<EduStudentCourseOperationRecord> aliased) {
        this(alias, aliased, null);
    }

    private EduStudentCourseOperation(Name alias, Table<EduStudentCourseOperationRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("学员课程操作记录表"), TableOptions.table());
    }

    /**
     * Create an aliased <code>lesson.edu_student_course_operation</code> table reference
     */
    public EduStudentCourseOperation(String alias) {
        this(DSL.name(alias), EDU_STUDENT_COURSE_OPERATION);
    }

    /**
     * Create an aliased <code>lesson.edu_student_course_operation</code> table reference
     */
    public EduStudentCourseOperation(Name alias) {
        this(alias, EDU_STUDENT_COURSE_OPERATION);
    }

    /**
     * Create a <code>lesson.edu_student_course_operation</code> table reference
     */
    public EduStudentCourseOperation() {
        this(DSL.name("edu_student_course_operation"), null);
    }

    public <O extends Record> EduStudentCourseOperation(Table<O> child, ForeignKey<O, EduStudentCourseOperationRecord> key) {
        super(child, key, EDU_STUDENT_COURSE_OPERATION);
    }

    @Override
    public Schema getSchema() {
        return Lesson.LESSON;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.<Index>asList(Indexes.EDU_STUDENT_COURSE_OPERATION_IDX_COURSE_ID, Indexes.EDU_STUDENT_COURSE_OPERATION_IDX_OPERATION_TIME, Indexes.EDU_STUDENT_COURSE_OPERATION_IDX_OPERATION_TYPE, Indexes.EDU_STUDENT_COURSE_OPERATION_IDX_STUDENT_ID);
    }

    @Override
    public Identity<EduStudentCourseOperationRecord, Long> getIdentity() {
        return (Identity<EduStudentCourseOperationRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<EduStudentCourseOperationRecord> getPrimaryKey() {
        return Keys.KEY_EDU_STUDENT_COURSE_OPERATION_PRIMARY;
    }

    @Override
    public List<UniqueKey<EduStudentCourseOperationRecord>> getKeys() {
        return Arrays.<UniqueKey<EduStudentCourseOperationRecord>>asList(Keys.KEY_EDU_STUDENT_COURSE_OPERATION_PRIMARY);
    }

    @Override
    public EduStudentCourseOperation as(String alias) {
        return new EduStudentCourseOperation(DSL.name(alias), this);
    }

    @Override
    public EduStudentCourseOperation as(Name alias) {
        return new EduStudentCourseOperation(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public EduStudentCourseOperation rename(String name) {
        return new EduStudentCourseOperation(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public EduStudentCourseOperation rename(Name name) {
        return new EduStudentCourseOperation(name, null);
    }

    // -------------------------------------------------------------------------
    // Row22 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row22<Long, Long, String, Long, String, String, String, Long, Long, Long, String, Long, String, BigDecimal, String, String, Long, String, LocalDateTime, LocalDateTime, LocalDateTime, Integer> fieldsRow() {
        return (Row22) super.fieldsRow();
    }
}
