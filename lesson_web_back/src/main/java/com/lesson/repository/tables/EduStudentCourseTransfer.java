/*
 * This file is generated by jOOQ.
 */
package com.lesson.repository.tables;


import com.lesson.repository.Indexes;
import com.lesson.repository.Keys;
import com.lesson.repository.Lesson;
import com.lesson.repository.tables.records.EduStudentCourseTransferRecord;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Identity;
import org.jooq.Index;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row13;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 学员转课记录表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class EduStudentCourseTransfer extends TableImpl<EduStudentCourseTransferRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>lesson.edu_student_course_transfer</code>
     */
    public static final EduStudentCourseTransfer EDU_STUDENT_COURSE_TRANSFER = new EduStudentCourseTransfer();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<EduStudentCourseTransferRecord> getRecordType() {
        return EduStudentCourseTransferRecord.class;
    }

    /**
     * The column <code>lesson.edu_student_course_transfer.id</code>. 记录ID
     */
    public final TableField<EduStudentCourseTransferRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "记录ID");

    /**
     * The column <code>lesson.edu_student_course_transfer.student_id</code>. 学员ID
     */
    public final TableField<EduStudentCourseTransferRecord, String> STUDENT_ID = createField(DSL.name("student_id"), SQLDataType.VARCHAR(32).nullable(false), this, "学员ID");

    /**
     * The column <code>lesson.edu_student_course_transfer.original_course_id</code>. 原课程ID
     */
    public final TableField<EduStudentCourseTransferRecord, Long> ORIGINAL_COURSE_ID = createField(DSL.name("original_course_id"), SQLDataType.BIGINT.nullable(false), this, "原课程ID");

    /**
     * The column <code>lesson.edu_student_course_transfer.target_course_id</code>. 目标课程ID
     */
    public final TableField<EduStudentCourseTransferRecord, Long> TARGET_COURSE_ID = createField(DSL.name("target_course_id"), SQLDataType.BIGINT.nullable(false), this, "目标课程ID");

    /**
     * The column <code>lesson.edu_student_course_transfer.transfer_hours</code>. 转课课时
     */
    public final TableField<EduStudentCourseTransferRecord, BigDecimal> TRANSFER_HOURS = createField(DSL.name("transfer_hours"), SQLDataType.DECIMAL(10, 2).nullable(false), this, "转课课时");

    /**
     * The column <code>lesson.edu_student_course_transfer.compensation_fee</code>. 补差价
     */
    public final TableField<EduStudentCourseTransferRecord, BigDecimal> COMPENSATION_FEE = createField(DSL.name("compensation_fee"), SQLDataType.DECIMAL(10, 2).nullable(false).defaultValue(DSL.inline("0.00", SQLDataType.DECIMAL)), this, "补差价");

    /**
     * The column <code>lesson.edu_student_course_transfer.valid_until</code>. 有效期至
     */
    public final TableField<EduStudentCourseTransferRecord, LocalDate> VALID_UNTIL = createField(DSL.name("valid_until"), SQLDataType.LOCALDATE.nullable(false), this, "有效期至");

    /**
     * The column <code>lesson.edu_student_course_transfer.reason</code>. 转课原因
     */
    public final TableField<EduStudentCourseTransferRecord, String> REASON = createField(DSL.name("reason"), SQLDataType.VARCHAR(500).nullable(false), this, "转课原因");

    /**
     * The column <code>lesson.edu_student_course_transfer.campus_id</code>. 校区ID
     */
    public final TableField<EduStudentCourseTransferRecord, Long> CAMPUS_ID = createField(DSL.name("campus_id"), SQLDataType.BIGINT.nullable(false), this, "校区ID");

    /**
     * The column <code>lesson.edu_student_course_transfer.institution_id</code>. 机构ID
     */
    public final TableField<EduStudentCourseTransferRecord, Long> INSTITUTION_ID = createField(DSL.name("institution_id"), SQLDataType.BIGINT.nullable(false), this, "机构ID");

    /**
     * The column <code>lesson.edu_student_course_transfer.created_time</code>. 创建时间
     */
    public final TableField<EduStudentCourseTransferRecord, LocalDateTime> CREATED_TIME = createField(DSL.name("created_time"), SQLDataType.LOCALDATETIME(0).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "创建时间");

    /**
     * The column <code>lesson.edu_student_course_transfer.update_time</code>. 更新时间
     */
    public final TableField<EduStudentCourseTransferRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(0).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "更新时间");

    /**
     * The column <code>lesson.edu_student_course_transfer.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public final TableField<EduStudentCourseTransferRecord, Integer> DELETED = createField(DSL.name("deleted"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否删除：0-未删除，1-已删除");

    private EduStudentCourseTransfer(Name alias, Table<EduStudentCourseTransferRecord> aliased) {
        this(alias, aliased, null);
    }

    private EduStudentCourseTransfer(Name alias, Table<EduStudentCourseTransferRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("学员转课记录表"), TableOptions.table());
    }

    /**
     * Create an aliased <code>lesson.edu_student_course_transfer</code> table reference
     */
    public EduStudentCourseTransfer(String alias) {
        this(DSL.name(alias), EDU_STUDENT_COURSE_TRANSFER);
    }

    /**
     * Create an aliased <code>lesson.edu_student_course_transfer</code> table reference
     */
    public EduStudentCourseTransfer(Name alias) {
        this(alias, EDU_STUDENT_COURSE_TRANSFER);
    }

    /**
     * Create a <code>lesson.edu_student_course_transfer</code> table reference
     */
    public EduStudentCourseTransfer() {
        this(DSL.name("edu_student_course_transfer"), null);
    }

    public <O extends Record> EduStudentCourseTransfer(Table<O> child, ForeignKey<O, EduStudentCourseTransferRecord> key) {
        super(child, key, EDU_STUDENT_COURSE_TRANSFER);
    }

    @Override
    public Schema getSchema() {
        return Lesson.LESSON;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.<Index>asList(Indexes.EDU_STUDENT_COURSE_TRANSFER_IDX_ORIGINAL_COURSE_ID, Indexes.EDU_STUDENT_COURSE_TRANSFER_IDX_STUDENT_ID, Indexes.EDU_STUDENT_COURSE_TRANSFER_IDX_TARGET_COURSE_ID);
    }

    @Override
    public Identity<EduStudentCourseTransferRecord, Long> getIdentity() {
        return (Identity<EduStudentCourseTransferRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<EduStudentCourseTransferRecord> getPrimaryKey() {
        return Keys.KEY_EDU_STUDENT_COURSE_TRANSFER_PRIMARY;
    }

    @Override
    public List<UniqueKey<EduStudentCourseTransferRecord>> getKeys() {
        return Arrays.<UniqueKey<EduStudentCourseTransferRecord>>asList(Keys.KEY_EDU_STUDENT_COURSE_TRANSFER_PRIMARY);
    }

    @Override
    public EduStudentCourseTransfer as(String alias) {
        return new EduStudentCourseTransfer(DSL.name(alias), this);
    }

    @Override
    public EduStudentCourseTransfer as(Name alias) {
        return new EduStudentCourseTransfer(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public EduStudentCourseTransfer rename(String name) {
        return new EduStudentCourseTransfer(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public EduStudentCourseTransfer rename(Name name) {
        return new EduStudentCourseTransfer(name, null);
    }

    // -------------------------------------------------------------------------
    // Row13 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row13<Long, String, Long, Long, BigDecimal, BigDecimal, LocalDate, String, Long, Long, LocalDateTime, LocalDateTime, Integer> fieldsRow() {
        return (Row13) super.fieldsRow();
    }
}
