/*
 * This file is generated by jOOQ.
 */
package com.lesson.repository.tables;


import com.lesson.repository.Indexes;
import com.lesson.repository.Keys;
import com.lesson.repository.Lesson;
import com.lesson.repository.tables.records.SysCoachSalaryRecord;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Identity;
import org.jooq.Index;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row12;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 教练薪资表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SysCoachSalary extends TableImpl<SysCoachSalaryRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>lesson.sys_coach_salary</code>
     */
    public static final SysCoachSalary SYS_COACH_SALARY = new SysCoachSalary();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SysCoachSalaryRecord> getRecordType() {
        return SysCoachSalaryRecord.class;
    }

    /**
     * The column <code>lesson.sys_coach_salary.id</code>. 主键ID
     */
    public final TableField<SysCoachSalaryRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "主键ID");

    /**
     * The column <code>lesson.sys_coach_salary.coach_id</code>. 关联教练ID
     */
    public final TableField<SysCoachSalaryRecord, Long> COACH_ID = createField(DSL.name("coach_id"), SQLDataType.BIGINT.nullable(false), this, "关联教练ID");

    /**
     * The column <code>lesson.sys_coach_salary.base_salary</code>. 基本工资
     */
    public final TableField<SysCoachSalaryRecord, BigDecimal> BASE_SALARY = createField(DSL.name("base_salary"), SQLDataType.DECIMAL(10, 2).nullable(false), this, "基本工资");

    /**
     * The column <code>lesson.sys_coach_salary.social_insurance</code>. 社保费
     */
    public final TableField<SysCoachSalaryRecord, BigDecimal> SOCIAL_INSURANCE = createField(DSL.name("social_insurance"), SQLDataType.DECIMAL(10, 2).nullable(false), this, "社保费");

    /**
     * The column <code>lesson.sys_coach_salary.class_fee</code>. 课时费
     */
    public final TableField<SysCoachSalaryRecord, BigDecimal> CLASS_FEE = createField(DSL.name("class_fee"), SQLDataType.DECIMAL(10, 2).nullable(false), this, "课时费");

    /**
     * The column <code>lesson.sys_coach_salary.performance_bonus</code>. 绩效奖金
     */
    public final TableField<SysCoachSalaryRecord, BigDecimal> PERFORMANCE_BONUS = createField(DSL.name("performance_bonus"), SQLDataType.DECIMAL(10, 2).defaultValue(DSL.inline("0.00", SQLDataType.DECIMAL)), this, "绩效奖金");

    /**
     * The column <code>lesson.sys_coach_salary.commission</code>. 提成百分比
     */
    public final TableField<SysCoachSalaryRecord, BigDecimal> COMMISSION = createField(DSL.name("commission"), SQLDataType.DECIMAL(5, 2).defaultValue(DSL.inline("0.00", SQLDataType.DECIMAL)), this, "提成百分比");

    /**
     * The column <code>lesson.sys_coach_salary.dividend</code>. 分红
     */
    public final TableField<SysCoachSalaryRecord, BigDecimal> DIVIDEND = createField(DSL.name("dividend"), SQLDataType.DECIMAL(10, 2).defaultValue(DSL.inline("0.00", SQLDataType.DECIMAL)), this, "分红");

    /**
     * The column <code>lesson.sys_coach_salary.effective_date</code>. 生效日期
     */
    public final TableField<SysCoachSalaryRecord, LocalDate> EFFECTIVE_DATE = createField(DSL.name("effective_date"), SQLDataType.LOCALDATE.nullable(false), this, "生效日期");

    /**
     * The column <code>lesson.sys_coach_salary.created_time</code>. 创建时间
     */
    public final TableField<SysCoachSalaryRecord, LocalDateTime> CREATED_TIME = createField(DSL.name("created_time"), SQLDataType.LOCALDATETIME(0).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "创建时间");

    /**
     * The column <code>lesson.sys_coach_salary.update_time</code>. 更新时间
     */
    public final TableField<SysCoachSalaryRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(0).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "更新时间");

    /**
     * The column <code>lesson.sys_coach_salary.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public final TableField<SysCoachSalaryRecord, Integer> DELETED = createField(DSL.name("deleted"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否删除：0-未删除，1-已删除");

    private SysCoachSalary(Name alias, Table<SysCoachSalaryRecord> aliased) {
        this(alias, aliased, null);
    }

    private SysCoachSalary(Name alias, Table<SysCoachSalaryRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("教练薪资表"), TableOptions.table());
    }

    /**
     * Create an aliased <code>lesson.sys_coach_salary</code> table reference
     */
    public SysCoachSalary(String alias) {
        this(DSL.name(alias), SYS_COACH_SALARY);
    }

    /**
     * Create an aliased <code>lesson.sys_coach_salary</code> table reference
     */
    public SysCoachSalary(Name alias) {
        this(alias, SYS_COACH_SALARY);
    }

    /**
     * Create a <code>lesson.sys_coach_salary</code> table reference
     */
    public SysCoachSalary() {
        this(DSL.name("sys_coach_salary"), null);
    }

    public <O extends Record> SysCoachSalary(Table<O> child, ForeignKey<O, SysCoachSalaryRecord> key) {
        super(child, key, SYS_COACH_SALARY);
    }

    @Override
    public Schema getSchema() {
        return Lesson.LESSON;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.<Index>asList(Indexes.SYS_COACH_SALARY_IDX_COACH_ID, Indexes.SYS_COACH_SALARY_IDX_EFFECTIVE_DATE);
    }

    @Override
    public Identity<SysCoachSalaryRecord, Long> getIdentity() {
        return (Identity<SysCoachSalaryRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<SysCoachSalaryRecord> getPrimaryKey() {
        return Keys.KEY_SYS_COACH_SALARY_PRIMARY;
    }

    @Override
    public List<UniqueKey<SysCoachSalaryRecord>> getKeys() {
        return Arrays.<UniqueKey<SysCoachSalaryRecord>>asList(Keys.KEY_SYS_COACH_SALARY_PRIMARY);
    }

    @Override
    public SysCoachSalary as(String alias) {
        return new SysCoachSalary(DSL.name(alias), this);
    }

    @Override
    public SysCoachSalary as(Name alias) {
        return new SysCoachSalary(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public SysCoachSalary rename(String name) {
        return new SysCoachSalary(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public SysCoachSalary rename(Name name) {
        return new SysCoachSalary(name, null);
    }

    // -------------------------------------------------------------------------
    // Row12 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row12<Long, Long, BigDecimal, BigDecimal, BigDecimal, BigDecimal, BigDecimal, BigDecimal, LocalDate, LocalDateTime, LocalDateTime, Integer> fieldsRow() {
        return (Row12) super.fieldsRow();
    }
}
