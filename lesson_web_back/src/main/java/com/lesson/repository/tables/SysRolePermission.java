/*
 * This file is generated by jOOQ.
 */
package com.lesson.repository.tables;


import com.lesson.repository.Indexes;
import com.lesson.repository.Keys;
import com.lesson.repository.Lesson;
import com.lesson.repository.tables.records.SysRolePermissionRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Identity;
import org.jooq.Index;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row6;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 角色权限关联表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SysRolePermission extends TableImpl<SysRolePermissionRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>lesson.sys_role_permission</code>
     */
    public static final SysRolePermission SYS_ROLE_PERMISSION = new SysRolePermission();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SysRolePermissionRecord> getRecordType() {
        return SysRolePermissionRecord.class;
    }

    /**
     * The column <code>lesson.sys_role_permission.id</code>. 主键ID
     */
    public final TableField<SysRolePermissionRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "主键ID");

    /**
     * The column <code>lesson.sys_role_permission.role_id</code>. 角色ID
     */
    public final TableField<SysRolePermissionRecord, Long> ROLE_ID = createField(DSL.name("role_id"), SQLDataType.BIGINT.nullable(false), this, "角色ID");

    /**
     * The column <code>lesson.sys_role_permission.permission</code>. 权限标识
     */
    public final TableField<SysRolePermissionRecord, String> PERMISSION = createField(DSL.name("permission"), SQLDataType.VARCHAR(100).nullable(false), this, "权限标识");

    /**
     * The column <code>lesson.sys_role_permission.created_time</code>. 创建时间
     */
    public final TableField<SysRolePermissionRecord, LocalDateTime> CREATED_TIME = createField(DSL.name("created_time"), SQLDataType.LOCALDATETIME(0).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "创建时间");

    /**
     * The column <code>lesson.sys_role_permission.update_time</code>. 更新时间
     */
    public final TableField<SysRolePermissionRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(0).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "更新时间");

    /**
     * The column <code>lesson.sys_role_permission.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public final TableField<SysRolePermissionRecord, Integer> DELETED = createField(DSL.name("deleted"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否删除：0-未删除，1-已删除");

    private SysRolePermission(Name alias, Table<SysRolePermissionRecord> aliased) {
        this(alias, aliased, null);
    }

    private SysRolePermission(Name alias, Table<SysRolePermissionRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("角色权限关联表"), TableOptions.table());
    }

    /**
     * Create an aliased <code>lesson.sys_role_permission</code> table reference
     */
    public SysRolePermission(String alias) {
        this(DSL.name(alias), SYS_ROLE_PERMISSION);
    }

    /**
     * Create an aliased <code>lesson.sys_role_permission</code> table reference
     */
    public SysRolePermission(Name alias) {
        this(alias, SYS_ROLE_PERMISSION);
    }

    /**
     * Create a <code>lesson.sys_role_permission</code> table reference
     */
    public SysRolePermission() {
        this(DSL.name("sys_role_permission"), null);
    }

    public <O extends Record> SysRolePermission(Table<O> child, ForeignKey<O, SysRolePermissionRecord> key) {
        super(child, key, SYS_ROLE_PERMISSION);
    }

    @Override
    public Schema getSchema() {
        return Lesson.LESSON;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.<Index>asList(Indexes.SYS_ROLE_PERMISSION_IDX_CREATED_TIME, Indexes.SYS_ROLE_PERMISSION_IDX_PERMISSION);
    }

    @Override
    public Identity<SysRolePermissionRecord, Long> getIdentity() {
        return (Identity<SysRolePermissionRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<SysRolePermissionRecord> getPrimaryKey() {
        return Keys.KEY_SYS_ROLE_PERMISSION_PRIMARY;
    }

    @Override
    public List<UniqueKey<SysRolePermissionRecord>> getKeys() {
        return Arrays.<UniqueKey<SysRolePermissionRecord>>asList(Keys.KEY_SYS_ROLE_PERMISSION_PRIMARY, Keys.KEY_SYS_ROLE_PERMISSION_UK_ROLE_PERMISSION);
    }

    @Override
    public SysRolePermission as(String alias) {
        return new SysRolePermission(DSL.name(alias), this);
    }

    @Override
    public SysRolePermission as(Name alias) {
        return new SysRolePermission(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public SysRolePermission rename(String name) {
        return new SysRolePermission(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public SysRolePermission rename(Name name) {
        return new SysRolePermission(name, null);
    }

    // -------------------------------------------------------------------------
    // Row6 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row6<Long, Long, String, LocalDateTime, LocalDateTime, Integer> fieldsRow() {
        return (Row6) super.fieldsRow();
    }
}
