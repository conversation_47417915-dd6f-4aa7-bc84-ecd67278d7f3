/*
 * This file is generated by jOOQ.
 */
package com.lesson.repository.tables;


import com.lesson.repository.Indexes;
import com.lesson.repository.Keys;
import com.lesson.repository.Lesson;
import com.lesson.repository.tables.records.EduCourseRecord;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Identity;
import org.jooq.Index;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row15;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 课程表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class EduCourse extends TableImpl<EduCourseRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>lesson.edu_course</code>
     */
    public static final EduCourse EDU_COURSE = new EduCourse();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<EduCourseRecord> getRecordType() {
        return EduCourseRecord.class;
    }

    /**
     * The column <code>lesson.edu_course.id</code>. 课程ID
     */
    public final TableField<EduCourseRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "课程ID");

    /**
     * The column <code>lesson.edu_course.name</code>. 课程名称
     */
    public final TableField<EduCourseRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(100).nullable(false), this, "课程名称");

    /**
     * The column <code>lesson.edu_course.type_id</code>. 课程类型(关联sys_constant表ID)
     */
    public final TableField<EduCourseRecord, Long> TYPE_ID = createField(DSL.name("type_id"), SQLDataType.BIGINT.nullable(false), this, "课程类型(关联sys_constant表ID)");

    /**
     * The column <code>lesson.edu_course.status</code>. 状态：DRAFT-草稿，PUBLISHED-已发布，SUSPENDED-已暂停，TERMINATED-已终止
     */
    public final TableField<EduCourseRecord, String> STATUS = createField(DSL.name("status"), SQLDataType.VARCHAR(20).nullable(false), this, "状态：DRAFT-草稿，PUBLISHED-已发布，SUSPENDED-已暂停，TERMINATED-已终止");

    /**
     * The column <code>lesson.edu_course.unit_hours</code>. 每次消耗课时数
     */
    public final TableField<EduCourseRecord, BigDecimal> UNIT_HOURS = createField(DSL.name("unit_hours"), SQLDataType.DECIMAL(10, 2).nullable(false).defaultValue(DSL.inline("1.00", SQLDataType.DECIMAL)), this, "每次消耗课时数");

    /**
     * The column <code>lesson.edu_course.total_hours</code>. 总课时数
     */
    public final TableField<EduCourseRecord, BigDecimal> TOTAL_HOURS = createField(DSL.name("total_hours"), SQLDataType.DECIMAL(10, 2).nullable(false).defaultValue(DSL.inline("0.00", SQLDataType.DECIMAL)), this, "总课时数");

    /**
     * The column <code>lesson.edu_course.consumed_hours</code>. 已消耗课时数
     */
    public final TableField<EduCourseRecord, BigDecimal> CONSUMED_HOURS = createField(DSL.name("consumed_hours"), SQLDataType.DECIMAL(10, 2).nullable(false).defaultValue(DSL.inline("0.00", SQLDataType.DECIMAL)), this, "已消耗课时数");

    /**
     * The column <code>lesson.edu_course.price</code>. 课程单价(元)
     */
    public final TableField<EduCourseRecord, BigDecimal> PRICE = createField(DSL.name("price"), SQLDataType.DECIMAL(10, 2).nullable(false).defaultValue(DSL.inline("0.00", SQLDataType.DECIMAL)), this, "课程单价(元)");

    /**
     * The column <code>lesson.edu_course.coach_fee</code>. 教练费用(元)
     */
    public final TableField<EduCourseRecord, BigDecimal> COACH_FEE = createField(DSL.name("coach_fee"), SQLDataType.DECIMAL(10, 2).nullable(false).defaultValue(DSL.inline("0.00", SQLDataType.DECIMAL)), this, "教练费用(元)");

    /**
     * The column <code>lesson.edu_course.description</code>. 课程描述
     */
    public final TableField<EduCourseRecord, String> DESCRIPTION = createField(DSL.name("description"), SQLDataType.CLOB, this, "课程描述");

    /**
     * The column <code>lesson.edu_course.campus_id</code>. 校区ID
     */
    public final TableField<EduCourseRecord, Long> CAMPUS_ID = createField(DSL.name("campus_id"), SQLDataType.BIGINT.nullable(false), this, "校区ID");

    /**
     * The column <code>lesson.edu_course.institution_id</code>. 机构ID
     */
    public final TableField<EduCourseRecord, Long> INSTITUTION_ID = createField(DSL.name("institution_id"), SQLDataType.BIGINT.nullable(false), this, "机构ID");

    /**
     * The column <code>lesson.edu_course.created_time</code>. 创建时间
     */
    public final TableField<EduCourseRecord, LocalDateTime> CREATED_TIME = createField(DSL.name("created_time"), SQLDataType.LOCALDATETIME(0).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "创建时间");

    /**
     * The column <code>lesson.edu_course.update_time</code>. 更新时间
     */
    public final TableField<EduCourseRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(0).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "更新时间");

    /**
     * The column <code>lesson.edu_course.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public final TableField<EduCourseRecord, Integer> DELETED = createField(DSL.name("deleted"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否删除：0-未删除，1-已删除");

    private EduCourse(Name alias, Table<EduCourseRecord> aliased) {
        this(alias, aliased, null);
    }

    private EduCourse(Name alias, Table<EduCourseRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("课程表"), TableOptions.table());
    }

    /**
     * Create an aliased <code>lesson.edu_course</code> table reference
     */
    public EduCourse(String alias) {
        this(DSL.name(alias), EDU_COURSE);
    }

    /**
     * Create an aliased <code>lesson.edu_course</code> table reference
     */
    public EduCourse(Name alias) {
        this(alias, EDU_COURSE);
    }

    /**
     * Create a <code>lesson.edu_course</code> table reference
     */
    public EduCourse() {
        this(DSL.name("edu_course"), null);
    }

    public <O extends Record> EduCourse(Table<O> child, ForeignKey<O, EduCourseRecord> key) {
        super(child, key, EDU_COURSE);
    }

    @Override
    public Schema getSchema() {
        return Lesson.LESSON;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.<Index>asList(Indexes.EDU_COURSE_IDX_CAMPUS_ID, Indexes.EDU_COURSE_IDX_INSTITUTION_ID, Indexes.EDU_COURSE_IDX_NAME, Indexes.EDU_COURSE_IDX_STATUS, Indexes.EDU_COURSE_IDX_TYPE_ID);
    }

    @Override
    public Identity<EduCourseRecord, Long> getIdentity() {
        return (Identity<EduCourseRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<EduCourseRecord> getPrimaryKey() {
        return Keys.KEY_EDU_COURSE_PRIMARY;
    }

    @Override
    public List<UniqueKey<EduCourseRecord>> getKeys() {
        return Arrays.<UniqueKey<EduCourseRecord>>asList(Keys.KEY_EDU_COURSE_PRIMARY, Keys.KEY_EDU_COURSE_IDX_UNIQUE_NAME_CAMPUS_INSTITUTION);
    }

    @Override
    public EduCourse as(String alias) {
        return new EduCourse(DSL.name(alias), this);
    }

    @Override
    public EduCourse as(Name alias) {
        return new EduCourse(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public EduCourse rename(String name) {
        return new EduCourse(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public EduCourse rename(Name name) {
        return new EduCourse(name, null);
    }

    // -------------------------------------------------------------------------
    // Row15 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row15<Long, String, Long, String, BigDecimal, BigDecimal, BigDecimal, BigDecimal, BigDecimal, String, Long, Long, LocalDateTime, LocalDateTime, Integer> fieldsRow() {
        return (Row15) super.fieldsRow();
    }
}
