/*
 * This file is generated by jOOQ.
 */
package com.lesson.repository.tables.records;


import com.lesson.repository.tables.EduStudentCourseTransfer;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record13;
import org.jooq.Row13;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 学员转课记录表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class EduStudentCourseTransferRecord extends UpdatableRecordImpl<EduStudentCourseTransferRecord> implements Record13<Long, String, Long, Long, BigDecimal, BigDecimal, LocalDate, String, Long, Long, LocalDateTime, LocalDateTime, Integer> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lesson.edu_student_course_transfer.id</code>. 记录ID
     */
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>lesson.edu_student_course_transfer.id</code>. 记录ID
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>lesson.edu_student_course_transfer.student_id</code>. 学员ID
     */
    public void setStudentId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>lesson.edu_student_course_transfer.student_id</code>. 学员ID
     */
    public String getStudentId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>lesson.edu_student_course_transfer.original_course_id</code>. 原课程ID
     */
    public void setOriginalCourseId(Long value) {
        set(2, value);
    }

    /**
     * Getter for <code>lesson.edu_student_course_transfer.original_course_id</code>. 原课程ID
     */
    public Long getOriginalCourseId() {
        return (Long) get(2);
    }

    /**
     * Setter for <code>lesson.edu_student_course_transfer.target_course_id</code>. 目标课程ID
     */
    public void setTargetCourseId(Long value) {
        set(3, value);
    }

    /**
     * Getter for <code>lesson.edu_student_course_transfer.target_course_id</code>. 目标课程ID
     */
    public Long getTargetCourseId() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>lesson.edu_student_course_transfer.transfer_hours</code>. 转课课时
     */
    public void setTransferHours(BigDecimal value) {
        set(4, value);
    }

    /**
     * Getter for <code>lesson.edu_student_course_transfer.transfer_hours</code>. 转课课时
     */
    public BigDecimal getTransferHours() {
        return (BigDecimal) get(4);
    }

    /**
     * Setter for <code>lesson.edu_student_course_transfer.compensation_fee</code>. 补差价
     */
    public void setCompensationFee(BigDecimal value) {
        set(5, value);
    }

    /**
     * Getter for <code>lesson.edu_student_course_transfer.compensation_fee</code>. 补差价
     */
    public BigDecimal getCompensationFee() {
        return (BigDecimal) get(5);
    }

    /**
     * Setter for <code>lesson.edu_student_course_transfer.valid_until</code>. 有效期至
     */
    public void setValidUntil(LocalDate value) {
        set(6, value);
    }

    /**
     * Getter for <code>lesson.edu_student_course_transfer.valid_until</code>. 有效期至
     */
    public LocalDate getValidUntil() {
        return (LocalDate) get(6);
    }

    /**
     * Setter for <code>lesson.edu_student_course_transfer.reason</code>. 转课原因
     */
    public void setReason(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>lesson.edu_student_course_transfer.reason</code>. 转课原因
     */
    public String getReason() {
        return (String) get(7);
    }

    /**
     * Setter for <code>lesson.edu_student_course_transfer.campus_id</code>. 校区ID
     */
    public void setCampusId(Long value) {
        set(8, value);
    }

    /**
     * Getter for <code>lesson.edu_student_course_transfer.campus_id</code>. 校区ID
     */
    public Long getCampusId() {
        return (Long) get(8);
    }

    /**
     * Setter for <code>lesson.edu_student_course_transfer.institution_id</code>. 机构ID
     */
    public void setInstitutionId(Long value) {
        set(9, value);
    }

    /**
     * Getter for <code>lesson.edu_student_course_transfer.institution_id</code>. 机构ID
     */
    public Long getInstitutionId() {
        return (Long) get(9);
    }

    /**
     * Setter for <code>lesson.edu_student_course_transfer.created_time</code>. 创建时间
     */
    public void setCreatedTime(LocalDateTime value) {
        set(10, value);
    }

    /**
     * Getter for <code>lesson.edu_student_course_transfer.created_time</code>. 创建时间
     */
    public LocalDateTime getCreatedTime() {
        return (LocalDateTime) get(10);
    }

    /**
     * Setter for <code>lesson.edu_student_course_transfer.update_time</code>. 更新时间
     */
    public void setUpdateTime(LocalDateTime value) {
        set(11, value);
    }

    /**
     * Getter for <code>lesson.edu_student_course_transfer.update_time</code>. 更新时间
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(11);
    }

    /**
     * Setter for <code>lesson.edu_student_course_transfer.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public void setDeleted(Integer value) {
        set(12, value);
    }

    /**
     * Getter for <code>lesson.edu_student_course_transfer.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public Integer getDeleted() {
        return (Integer) get(12);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record13 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row13<Long, String, Long, Long, BigDecimal, BigDecimal, LocalDate, String, Long, Long, LocalDateTime, LocalDateTime, Integer> fieldsRow() {
        return (Row13) super.fieldsRow();
    }

    @Override
    public Row13<Long, String, Long, Long, BigDecimal, BigDecimal, LocalDate, String, Long, Long, LocalDateTime, LocalDateTime, Integer> valuesRow() {
        return (Row13) super.valuesRow();
    }

    @Override
    public Field<Long> field1() {
        return EduStudentCourseTransfer.EDU_STUDENT_COURSE_TRANSFER.ID;
    }

    @Override
    public Field<String> field2() {
        return EduStudentCourseTransfer.EDU_STUDENT_COURSE_TRANSFER.STUDENT_ID;
    }

    @Override
    public Field<Long> field3() {
        return EduStudentCourseTransfer.EDU_STUDENT_COURSE_TRANSFER.ORIGINAL_COURSE_ID;
    }

    @Override
    public Field<Long> field4() {
        return EduStudentCourseTransfer.EDU_STUDENT_COURSE_TRANSFER.TARGET_COURSE_ID;
    }

    @Override
    public Field<BigDecimal> field5() {
        return EduStudentCourseTransfer.EDU_STUDENT_COURSE_TRANSFER.TRANSFER_HOURS;
    }

    @Override
    public Field<BigDecimal> field6() {
        return EduStudentCourseTransfer.EDU_STUDENT_COURSE_TRANSFER.COMPENSATION_FEE;
    }

    @Override
    public Field<LocalDate> field7() {
        return EduStudentCourseTransfer.EDU_STUDENT_COURSE_TRANSFER.VALID_UNTIL;
    }

    @Override
    public Field<String> field8() {
        return EduStudentCourseTransfer.EDU_STUDENT_COURSE_TRANSFER.REASON;
    }

    @Override
    public Field<Long> field9() {
        return EduStudentCourseTransfer.EDU_STUDENT_COURSE_TRANSFER.CAMPUS_ID;
    }

    @Override
    public Field<Long> field10() {
        return EduStudentCourseTransfer.EDU_STUDENT_COURSE_TRANSFER.INSTITUTION_ID;
    }

    @Override
    public Field<LocalDateTime> field11() {
        return EduStudentCourseTransfer.EDU_STUDENT_COURSE_TRANSFER.CREATED_TIME;
    }

    @Override
    public Field<LocalDateTime> field12() {
        return EduStudentCourseTransfer.EDU_STUDENT_COURSE_TRANSFER.UPDATE_TIME;
    }

    @Override
    public Field<Integer> field13() {
        return EduStudentCourseTransfer.EDU_STUDENT_COURSE_TRANSFER.DELETED;
    }

    @Override
    public Long component1() {
        return getId();
    }

    @Override
    public String component2() {
        return getStudentId();
    }

    @Override
    public Long component3() {
        return getOriginalCourseId();
    }

    @Override
    public Long component4() {
        return getTargetCourseId();
    }

    @Override
    public BigDecimal component5() {
        return getTransferHours();
    }

    @Override
    public BigDecimal component6() {
        return getCompensationFee();
    }

    @Override
    public LocalDate component7() {
        return getValidUntil();
    }

    @Override
    public String component8() {
        return getReason();
    }

    @Override
    public Long component9() {
        return getCampusId();
    }

    @Override
    public Long component10() {
        return getInstitutionId();
    }

    @Override
    public LocalDateTime component11() {
        return getCreatedTime();
    }

    @Override
    public LocalDateTime component12() {
        return getUpdateTime();
    }

    @Override
    public Integer component13() {
        return getDeleted();
    }

    @Override
    public Long value1() {
        return getId();
    }

    @Override
    public String value2() {
        return getStudentId();
    }

    @Override
    public Long value3() {
        return getOriginalCourseId();
    }

    @Override
    public Long value4() {
        return getTargetCourseId();
    }

    @Override
    public BigDecimal value5() {
        return getTransferHours();
    }

    @Override
    public BigDecimal value6() {
        return getCompensationFee();
    }

    @Override
    public LocalDate value7() {
        return getValidUntil();
    }

    @Override
    public String value8() {
        return getReason();
    }

    @Override
    public Long value9() {
        return getCampusId();
    }

    @Override
    public Long value10() {
        return getInstitutionId();
    }

    @Override
    public LocalDateTime value11() {
        return getCreatedTime();
    }

    @Override
    public LocalDateTime value12() {
        return getUpdateTime();
    }

    @Override
    public Integer value13() {
        return getDeleted();
    }

    @Override
    public EduStudentCourseTransferRecord value1(Long value) {
        setId(value);
        return this;
    }

    @Override
    public EduStudentCourseTransferRecord value2(String value) {
        setStudentId(value);
        return this;
    }

    @Override
    public EduStudentCourseTransferRecord value3(Long value) {
        setOriginalCourseId(value);
        return this;
    }

    @Override
    public EduStudentCourseTransferRecord value4(Long value) {
        setTargetCourseId(value);
        return this;
    }

    @Override
    public EduStudentCourseTransferRecord value5(BigDecimal value) {
        setTransferHours(value);
        return this;
    }

    @Override
    public EduStudentCourseTransferRecord value6(BigDecimal value) {
        setCompensationFee(value);
        return this;
    }

    @Override
    public EduStudentCourseTransferRecord value7(LocalDate value) {
        setValidUntil(value);
        return this;
    }

    @Override
    public EduStudentCourseTransferRecord value8(String value) {
        setReason(value);
        return this;
    }

    @Override
    public EduStudentCourseTransferRecord value9(Long value) {
        setCampusId(value);
        return this;
    }

    @Override
    public EduStudentCourseTransferRecord value10(Long value) {
        setInstitutionId(value);
        return this;
    }

    @Override
    public EduStudentCourseTransferRecord value11(LocalDateTime value) {
        setCreatedTime(value);
        return this;
    }

    @Override
    public EduStudentCourseTransferRecord value12(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public EduStudentCourseTransferRecord value13(Integer value) {
        setDeleted(value);
        return this;
    }

    @Override
    public EduStudentCourseTransferRecord values(Long value1, String value2, Long value3, Long value4, BigDecimal value5, BigDecimal value6, LocalDate value7, String value8, Long value9, Long value10, LocalDateTime value11, LocalDateTime value12, Integer value13) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached EduStudentCourseTransferRecord
     */
    public EduStudentCourseTransferRecord() {
        super(EduStudentCourseTransfer.EDU_STUDENT_COURSE_TRANSFER);
    }

    /**
     * Create a detached, initialised EduStudentCourseTransferRecord
     */
    public EduStudentCourseTransferRecord(Long id, String studentId, Long originalCourseId, Long targetCourseId, BigDecimal transferHours, BigDecimal compensationFee, LocalDate validUntil, String reason, Long campusId, Long institutionId, LocalDateTime createdTime, LocalDateTime updateTime, Integer deleted) {
        super(EduStudentCourseTransfer.EDU_STUDENT_COURSE_TRANSFER);

        setId(id);
        setStudentId(studentId);
        setOriginalCourseId(originalCourseId);
        setTargetCourseId(targetCourseId);
        setTransferHours(transferHours);
        setCompensationFee(compensationFee);
        setValidUntil(validUntil);
        setReason(reason);
        setCampusId(campusId);
        setInstitutionId(institutionId);
        setCreatedTime(createdTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
    }
}
