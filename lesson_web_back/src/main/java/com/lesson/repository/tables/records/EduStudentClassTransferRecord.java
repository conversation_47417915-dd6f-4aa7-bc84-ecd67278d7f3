/*
 * This file is generated by jOOQ.
 */
package com.lesson.repository.tables.records;


import com.lesson.repository.tables.EduStudentClassTransfer;

import java.time.LocalDateTime;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record11;
import org.jooq.Row11;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 学员转班记录表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class EduStudentClassTransferRecord extends UpdatableRecordImpl<EduStudentClassTransferRecord> implements Record11<Long, String, String, String, String, String, Long, Long, LocalDateTime, LocalDateTime, Integer> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lesson.edu_student_class_transfer.id</code>. 记录ID
     */
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>lesson.edu_student_class_transfer.id</code>. 记录ID
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>lesson.edu_student_class_transfer.student_id</code>. 学员ID
     */
    public void setStudentId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>lesson.edu_student_class_transfer.student_id</code>. 学员ID
     */
    public String getStudentId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>lesson.edu_student_class_transfer.course_id</code>. 课程ID
     */
    public void setCourseId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>lesson.edu_student_class_transfer.course_id</code>. 课程ID
     */
    public String getCourseId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>lesson.edu_student_class_transfer.original_schedule</code>. 原上课时间
     */
    public void setOriginalSchedule(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>lesson.edu_student_class_transfer.original_schedule</code>. 原上课时间
     */
    public String getOriginalSchedule() {
        return (String) get(3);
    }

    /**
     * Setter for <code>lesson.edu_student_class_transfer.new_schedule</code>. 新上课时间
     */
    public void setNewSchedule(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>lesson.edu_student_class_transfer.new_schedule</code>. 新上课时间
     */
    public String getNewSchedule() {
        return (String) get(4);
    }

    /**
     * Setter for <code>lesson.edu_student_class_transfer.reason</code>. 转班原因
     */
    public void setReason(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>lesson.edu_student_class_transfer.reason</code>. 转班原因
     */
    public String getReason() {
        return (String) get(5);
    }

    /**
     * Setter for <code>lesson.edu_student_class_transfer.campus_id</code>. 校区ID
     */
    public void setCampusId(Long value) {
        set(6, value);
    }

    /**
     * Getter for <code>lesson.edu_student_class_transfer.campus_id</code>. 校区ID
     */
    public Long getCampusId() {
        return (Long) get(6);
    }

    /**
     * Setter for <code>lesson.edu_student_class_transfer.institution_id</code>. 机构ID
     */
    public void setInstitutionId(Long value) {
        set(7, value);
    }

    /**
     * Getter for <code>lesson.edu_student_class_transfer.institution_id</code>. 机构ID
     */
    public Long getInstitutionId() {
        return (Long) get(7);
    }

    /**
     * Setter for <code>lesson.edu_student_class_transfer.created_time</code>. 创建时间
     */
    public void setCreatedTime(LocalDateTime value) {
        set(8, value);
    }

    /**
     * Getter for <code>lesson.edu_student_class_transfer.created_time</code>. 创建时间
     */
    public LocalDateTime getCreatedTime() {
        return (LocalDateTime) get(8);
    }

    /**
     * Setter for <code>lesson.edu_student_class_transfer.update_time</code>. 更新时间
     */
    public void setUpdateTime(LocalDateTime value) {
        set(9, value);
    }

    /**
     * Getter for <code>lesson.edu_student_class_transfer.update_time</code>. 更新时间
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(9);
    }

    /**
     * Setter for <code>lesson.edu_student_class_transfer.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public void setDeleted(Integer value) {
        set(10, value);
    }

    /**
     * Getter for <code>lesson.edu_student_class_transfer.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public Integer getDeleted() {
        return (Integer) get(10);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record11 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row11<Long, String, String, String, String, String, Long, Long, LocalDateTime, LocalDateTime, Integer> fieldsRow() {
        return (Row11) super.fieldsRow();
    }

    @Override
    public Row11<Long, String, String, String, String, String, Long, Long, LocalDateTime, LocalDateTime, Integer> valuesRow() {
        return (Row11) super.valuesRow();
    }

    @Override
    public Field<Long> field1() {
        return EduStudentClassTransfer.EDU_STUDENT_CLASS_TRANSFER.ID;
    }

    @Override
    public Field<String> field2() {
        return EduStudentClassTransfer.EDU_STUDENT_CLASS_TRANSFER.STUDENT_ID;
    }

    @Override
    public Field<String> field3() {
        return EduStudentClassTransfer.EDU_STUDENT_CLASS_TRANSFER.COURSE_ID;
    }

    @Override
    public Field<String> field4() {
        return EduStudentClassTransfer.EDU_STUDENT_CLASS_TRANSFER.ORIGINAL_SCHEDULE;
    }

    @Override
    public Field<String> field5() {
        return EduStudentClassTransfer.EDU_STUDENT_CLASS_TRANSFER.NEW_SCHEDULE;
    }

    @Override
    public Field<String> field6() {
        return EduStudentClassTransfer.EDU_STUDENT_CLASS_TRANSFER.REASON;
    }

    @Override
    public Field<Long> field7() {
        return EduStudentClassTransfer.EDU_STUDENT_CLASS_TRANSFER.CAMPUS_ID;
    }

    @Override
    public Field<Long> field8() {
        return EduStudentClassTransfer.EDU_STUDENT_CLASS_TRANSFER.INSTITUTION_ID;
    }

    @Override
    public Field<LocalDateTime> field9() {
        return EduStudentClassTransfer.EDU_STUDENT_CLASS_TRANSFER.CREATED_TIME;
    }

    @Override
    public Field<LocalDateTime> field10() {
        return EduStudentClassTransfer.EDU_STUDENT_CLASS_TRANSFER.UPDATE_TIME;
    }

    @Override
    public Field<Integer> field11() {
        return EduStudentClassTransfer.EDU_STUDENT_CLASS_TRANSFER.DELETED;
    }

    @Override
    public Long component1() {
        return getId();
    }

    @Override
    public String component2() {
        return getStudentId();
    }

    @Override
    public String component3() {
        return getCourseId();
    }

    @Override
    public String component4() {
        return getOriginalSchedule();
    }

    @Override
    public String component5() {
        return getNewSchedule();
    }

    @Override
    public String component6() {
        return getReason();
    }

    @Override
    public Long component7() {
        return getCampusId();
    }

    @Override
    public Long component8() {
        return getInstitutionId();
    }

    @Override
    public LocalDateTime component9() {
        return getCreatedTime();
    }

    @Override
    public LocalDateTime component10() {
        return getUpdateTime();
    }

    @Override
    public Integer component11() {
        return getDeleted();
    }

    @Override
    public Long value1() {
        return getId();
    }

    @Override
    public String value2() {
        return getStudentId();
    }

    @Override
    public String value3() {
        return getCourseId();
    }

    @Override
    public String value4() {
        return getOriginalSchedule();
    }

    @Override
    public String value5() {
        return getNewSchedule();
    }

    @Override
    public String value6() {
        return getReason();
    }

    @Override
    public Long value7() {
        return getCampusId();
    }

    @Override
    public Long value8() {
        return getInstitutionId();
    }

    @Override
    public LocalDateTime value9() {
        return getCreatedTime();
    }

    @Override
    public LocalDateTime value10() {
        return getUpdateTime();
    }

    @Override
    public Integer value11() {
        return getDeleted();
    }

    @Override
    public EduStudentClassTransferRecord value1(Long value) {
        setId(value);
        return this;
    }

    @Override
    public EduStudentClassTransferRecord value2(String value) {
        setStudentId(value);
        return this;
    }

    @Override
    public EduStudentClassTransferRecord value3(String value) {
        setCourseId(value);
        return this;
    }

    @Override
    public EduStudentClassTransferRecord value4(String value) {
        setOriginalSchedule(value);
        return this;
    }

    @Override
    public EduStudentClassTransferRecord value5(String value) {
        setNewSchedule(value);
        return this;
    }

    @Override
    public EduStudentClassTransferRecord value6(String value) {
        setReason(value);
        return this;
    }

    @Override
    public EduStudentClassTransferRecord value7(Long value) {
        setCampusId(value);
        return this;
    }

    @Override
    public EduStudentClassTransferRecord value8(Long value) {
        setInstitutionId(value);
        return this;
    }

    @Override
    public EduStudentClassTransferRecord value9(LocalDateTime value) {
        setCreatedTime(value);
        return this;
    }

    @Override
    public EduStudentClassTransferRecord value10(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public EduStudentClassTransferRecord value11(Integer value) {
        setDeleted(value);
        return this;
    }

    @Override
    public EduStudentClassTransferRecord values(Long value1, String value2, String value3, String value4, String value5, String value6, Long value7, Long value8, LocalDateTime value9, LocalDateTime value10, Integer value11) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached EduStudentClassTransferRecord
     */
    public EduStudentClassTransferRecord() {
        super(EduStudentClassTransfer.EDU_STUDENT_CLASS_TRANSFER);
    }

    /**
     * Create a detached, initialised EduStudentClassTransferRecord
     */
    public EduStudentClassTransferRecord(Long id, String studentId, String courseId, String originalSchedule, String newSchedule, String reason, Long campusId, Long institutionId, LocalDateTime createdTime, LocalDateTime updateTime, Integer deleted) {
        super(EduStudentClassTransfer.EDU_STUDENT_CLASS_TRANSFER);

        setId(id);
        setStudentId(studentId);
        setCourseId(courseId);
        setOriginalSchedule(originalSchedule);
        setNewSchedule(newSchedule);
        setReason(reason);
        setCampusId(campusId);
        setInstitutionId(institutionId);
        setCreatedTime(createdTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
    }
}
