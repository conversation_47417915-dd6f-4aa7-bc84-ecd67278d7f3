/*
 * This file is generated by jOOQ.
 */
package com.lesson.repository.tables.records;


import com.lesson.repository.tables.EduStudent;

import java.time.LocalDateTime;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record12;
import org.jooq.Row12;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 学员表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class EduStudentRecord extends UpdatableRecordImpl<EduStudentRecord> implements Record12<Long, String, String, Integer, String, Long, Long, Long, String, LocalDateTime, LocalDateTime, Integer> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lesson.edu_student.id</code>. 记录ID
     */
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>lesson.edu_student.id</code>. 记录ID
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>lesson.edu_student.name</code>. 学员姓名
     */
    public void setName(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>lesson.edu_student.name</code>. 学员姓名
     */
    public String getName() {
        return (String) get(1);
    }

    /**
     * Setter for <code>lesson.edu_student.gender</code>. 性别：MALE-男，FEMALE-女
     */
    public void setGender(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>lesson.edu_student.gender</code>. 性别：MALE-男，FEMALE-女
     */
    public String getGender() {
        return (String) get(2);
    }

    /**
     * Setter for <code>lesson.edu_student.age</code>. 年龄
     */
    public void setAge(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>lesson.edu_student.age</code>. 年龄
     */
    public Integer getAge() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>lesson.edu_student.phone</code>. 联系电话
     */
    public void setPhone(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>lesson.edu_student.phone</code>. 联系电话
     */
    public String getPhone() {
        return (String) get(4);
    }

    /**
     * Setter for <code>lesson.edu_student.campus_id</code>. 校区ID
     */
    public void setCampusId(Long value) {
        set(5, value);
    }

    /**
     * Getter for <code>lesson.edu_student.campus_id</code>. 校区ID
     */
    public Long getCampusId() {
        return (Long) get(5);
    }

    /**
     * Setter for <code>lesson.edu_student.institution_id</code>. 机构ID
     */
    public void setInstitutionId(Long value) {
        set(6, value);
    }

    /**
     * Getter for <code>lesson.edu_student.institution_id</code>. 机构ID
     */
    public Long getInstitutionId() {
        return (Long) get(6);
    }

    /**
     * Setter for <code>lesson.edu_student.source_id</code>. 学员来源ID（关联sys_constant表）
     */
    public void setSourceId(Long value) {
        set(7, value);
    }

    /**
     * Getter for <code>lesson.edu_student.source_id</code>. 学员来源ID（关联sys_constant表）
     */
    public Long getSourceId() {
        return (Long) get(7);
    }

    /**
     * Setter for <code>lesson.edu_student.status</code>. 状态：STUDYING-在学，SUSPENDED-停课，GRADUATED-结业
     */
    public void setStatus(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>lesson.edu_student.status</code>. 状态：STUDYING-在学，SUSPENDED-停课，GRADUATED-结业
     */
    public String getStatus() {
        return (String) get(8);
    }

    /**
     * Setter for <code>lesson.edu_student.created_time</code>. 创建时间
     */
    public void setCreatedTime(LocalDateTime value) {
        set(9, value);
    }

    /**
     * Getter for <code>lesson.edu_student.created_time</code>. 创建时间
     */
    public LocalDateTime getCreatedTime() {
        return (LocalDateTime) get(9);
    }

    /**
     * Setter for <code>lesson.edu_student.update_time</code>. 更新时间
     */
    public void setUpdateTime(LocalDateTime value) {
        set(10, value);
    }

    /**
     * Getter for <code>lesson.edu_student.update_time</code>. 更新时间
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(10);
    }

    /**
     * Setter for <code>lesson.edu_student.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public void setDeleted(Integer value) {
        set(11, value);
    }

    /**
     * Getter for <code>lesson.edu_student.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public Integer getDeleted() {
        return (Integer) get(11);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record12 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row12<Long, String, String, Integer, String, Long, Long, Long, String, LocalDateTime, LocalDateTime, Integer> fieldsRow() {
        return (Row12) super.fieldsRow();
    }

    @Override
    public Row12<Long, String, String, Integer, String, Long, Long, Long, String, LocalDateTime, LocalDateTime, Integer> valuesRow() {
        return (Row12) super.valuesRow();
    }

    @Override
    public Field<Long> field1() {
        return EduStudent.EDU_STUDENT.ID;
    }

    @Override
    public Field<String> field2() {
        return EduStudent.EDU_STUDENT.NAME;
    }

    @Override
    public Field<String> field3() {
        return EduStudent.EDU_STUDENT.GENDER;
    }

    @Override
    public Field<Integer> field4() {
        return EduStudent.EDU_STUDENT.AGE;
    }

    @Override
    public Field<String> field5() {
        return EduStudent.EDU_STUDENT.PHONE;
    }

    @Override
    public Field<Long> field6() {
        return EduStudent.EDU_STUDENT.CAMPUS_ID;
    }

    @Override
    public Field<Long> field7() {
        return EduStudent.EDU_STUDENT.INSTITUTION_ID;
    }

    @Override
    public Field<Long> field8() {
        return EduStudent.EDU_STUDENT.SOURCE_ID;
    }

    @Override
    public Field<String> field9() {
        return EduStudent.EDU_STUDENT.STATUS;
    }

    @Override
    public Field<LocalDateTime> field10() {
        return EduStudent.EDU_STUDENT.CREATED_TIME;
    }

    @Override
    public Field<LocalDateTime> field11() {
        return EduStudent.EDU_STUDENT.UPDATE_TIME;
    }

    @Override
    public Field<Integer> field12() {
        return EduStudent.EDU_STUDENT.DELETED;
    }

    @Override
    public Long component1() {
        return getId();
    }

    @Override
    public String component2() {
        return getName();
    }

    @Override
    public String component3() {
        return getGender();
    }

    @Override
    public Integer component4() {
        return getAge();
    }

    @Override
    public String component5() {
        return getPhone();
    }

    @Override
    public Long component6() {
        return getCampusId();
    }

    @Override
    public Long component7() {
        return getInstitutionId();
    }

    @Override
    public Long component8() {
        return getSourceId();
    }

    @Override
    public String component9() {
        return getStatus();
    }

    @Override
    public LocalDateTime component10() {
        return getCreatedTime();
    }

    @Override
    public LocalDateTime component11() {
        return getUpdateTime();
    }

    @Override
    public Integer component12() {
        return getDeleted();
    }

    @Override
    public Long value1() {
        return getId();
    }

    @Override
    public String value2() {
        return getName();
    }

    @Override
    public String value3() {
        return getGender();
    }

    @Override
    public Integer value4() {
        return getAge();
    }

    @Override
    public String value5() {
        return getPhone();
    }

    @Override
    public Long value6() {
        return getCampusId();
    }

    @Override
    public Long value7() {
        return getInstitutionId();
    }

    @Override
    public Long value8() {
        return getSourceId();
    }

    @Override
    public String value9() {
        return getStatus();
    }

    @Override
    public LocalDateTime value10() {
        return getCreatedTime();
    }

    @Override
    public LocalDateTime value11() {
        return getUpdateTime();
    }

    @Override
    public Integer value12() {
        return getDeleted();
    }

    @Override
    public EduStudentRecord value1(Long value) {
        setId(value);
        return this;
    }

    @Override
    public EduStudentRecord value2(String value) {
        setName(value);
        return this;
    }

    @Override
    public EduStudentRecord value3(String value) {
        setGender(value);
        return this;
    }

    @Override
    public EduStudentRecord value4(Integer value) {
        setAge(value);
        return this;
    }

    @Override
    public EduStudentRecord value5(String value) {
        setPhone(value);
        return this;
    }

    @Override
    public EduStudentRecord value6(Long value) {
        setCampusId(value);
        return this;
    }

    @Override
    public EduStudentRecord value7(Long value) {
        setInstitutionId(value);
        return this;
    }

    @Override
    public EduStudentRecord value8(Long value) {
        setSourceId(value);
        return this;
    }

    @Override
    public EduStudentRecord value9(String value) {
        setStatus(value);
        return this;
    }

    @Override
    public EduStudentRecord value10(LocalDateTime value) {
        setCreatedTime(value);
        return this;
    }

    @Override
    public EduStudentRecord value11(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public EduStudentRecord value12(Integer value) {
        setDeleted(value);
        return this;
    }

    @Override
    public EduStudentRecord values(Long value1, String value2, String value3, Integer value4, String value5, Long value6, Long value7, Long value8, String value9, LocalDateTime value10, LocalDateTime value11, Integer value12) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached EduStudentRecord
     */
    public EduStudentRecord() {
        super(EduStudent.EDU_STUDENT);
    }

    /**
     * Create a detached, initialised EduStudentRecord
     */
    public EduStudentRecord(Long id, String name, String gender, Integer age, String phone, Long campusId, Long institutionId, Long sourceId, String status, LocalDateTime createdTime, LocalDateTime updateTime, Integer deleted) {
        super(EduStudent.EDU_STUDENT);

        setId(id);
        setName(name);
        setGender(gender);
        setAge(age);
        setPhone(phone);
        setCampusId(campusId);
        setInstitutionId(institutionId);
        setSourceId(sourceId);
        setStatus(status);
        setCreatedTime(createdTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
    }
}
