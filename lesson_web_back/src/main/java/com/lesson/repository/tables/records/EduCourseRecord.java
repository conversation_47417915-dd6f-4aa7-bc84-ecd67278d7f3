/*
 * This file is generated by jOOQ.
 */
package com.lesson.repository.tables.records;


import com.lesson.repository.tables.EduCourse;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record15;
import org.jooq.Row15;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 课程表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class EduCourseRecord extends UpdatableRecordImpl<EduCourseRecord> implements Record15<Long, String, Long, String, BigDecimal, BigDecimal, BigDecimal, BigDecimal, BigDecimal, String, Long, Long, LocalDateTime, LocalDateTime, Integer> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lesson.edu_course.id</code>. 课程ID
     */
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>lesson.edu_course.id</code>. 课程ID
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>lesson.edu_course.name</code>. 课程名称
     */
    public void setName(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>lesson.edu_course.name</code>. 课程名称
     */
    public String getName() {
        return (String) get(1);
    }

    /**
     * Setter for <code>lesson.edu_course.type_id</code>. 课程类型(关联sys_constant表ID)
     */
    public void setTypeId(Long value) {
        set(2, value);
    }

    /**
     * Getter for <code>lesson.edu_course.type_id</code>. 课程类型(关联sys_constant表ID)
     */
    public Long getTypeId() {
        return (Long) get(2);
    }

    /**
     * Setter for <code>lesson.edu_course.status</code>. 状态：DRAFT-草稿，PUBLISHED-已发布，SUSPENDED-已暂停，TERMINATED-已终止
     */
    public void setStatus(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>lesson.edu_course.status</code>. 状态：DRAFT-草稿，PUBLISHED-已发布，SUSPENDED-已暂停，TERMINATED-已终止
     */
    public String getStatus() {
        return (String) get(3);
    }

    /**
     * Setter for <code>lesson.edu_course.unit_hours</code>. 每次消耗课时数
     */
    public void setUnitHours(BigDecimal value) {
        set(4, value);
    }

    /**
     * Getter for <code>lesson.edu_course.unit_hours</code>. 每次消耗课时数
     */
    public BigDecimal getUnitHours() {
        return (BigDecimal) get(4);
    }

    /**
     * Setter for <code>lesson.edu_course.total_hours</code>. 总课时数
     */
    public void setTotalHours(BigDecimal value) {
        set(5, value);
    }

    /**
     * Getter for <code>lesson.edu_course.total_hours</code>. 总课时数
     */
    public BigDecimal getTotalHours() {
        return (BigDecimal) get(5);
    }

    /**
     * Setter for <code>lesson.edu_course.consumed_hours</code>. 已消耗课时数
     */
    public void setConsumedHours(BigDecimal value) {
        set(6, value);
    }

    /**
     * Getter for <code>lesson.edu_course.consumed_hours</code>. 已消耗课时数
     */
    public BigDecimal getConsumedHours() {
        return (BigDecimal) get(6);
    }

    /**
     * Setter for <code>lesson.edu_course.price</code>. 课程单价(元)
     */
    public void setPrice(BigDecimal value) {
        set(7, value);
    }

    /**
     * Getter for <code>lesson.edu_course.price</code>. 课程单价(元)
     */
    public BigDecimal getPrice() {
        return (BigDecimal) get(7);
    }

    /**
     * Setter for <code>lesson.edu_course.coach_fee</code>. 教练费用(元)
     */
    public void setCoachFee(BigDecimal value) {
        set(8, value);
    }

    /**
     * Getter for <code>lesson.edu_course.coach_fee</code>. 教练费用(元)
     */
    public BigDecimal getCoachFee() {
        return (BigDecimal) get(8);
    }

    /**
     * Setter for <code>lesson.edu_course.description</code>. 课程描述
     */
    public void setDescription(String value) {
        set(9, value);
    }

    /**
     * Getter for <code>lesson.edu_course.description</code>. 课程描述
     */
    public String getDescription() {
        return (String) get(9);
    }

    /**
     * Setter for <code>lesson.edu_course.campus_id</code>. 校区ID
     */
    public void setCampusId(Long value) {
        set(10, value);
    }

    /**
     * Getter for <code>lesson.edu_course.campus_id</code>. 校区ID
     */
    public Long getCampusId() {
        return (Long) get(10);
    }

    /**
     * Setter for <code>lesson.edu_course.institution_id</code>. 机构ID
     */
    public void setInstitutionId(Long value) {
        set(11, value);
    }

    /**
     * Getter for <code>lesson.edu_course.institution_id</code>. 机构ID
     */
    public Long getInstitutionId() {
        return (Long) get(11);
    }

    /**
     * Setter for <code>lesson.edu_course.created_time</code>. 创建时间
     */
    public void setCreatedTime(LocalDateTime value) {
        set(12, value);
    }

    /**
     * Getter for <code>lesson.edu_course.created_time</code>. 创建时间
     */
    public LocalDateTime getCreatedTime() {
        return (LocalDateTime) get(12);
    }

    /**
     * Setter for <code>lesson.edu_course.update_time</code>. 更新时间
     */
    public void setUpdateTime(LocalDateTime value) {
        set(13, value);
    }

    /**
     * Getter for <code>lesson.edu_course.update_time</code>. 更新时间
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(13);
    }

    /**
     * Setter for <code>lesson.edu_course.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public void setDeleted(Integer value) {
        set(14, value);
    }

    /**
     * Getter for <code>lesson.edu_course.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public Integer getDeleted() {
        return (Integer) get(14);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record15 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row15<Long, String, Long, String, BigDecimal, BigDecimal, BigDecimal, BigDecimal, BigDecimal, String, Long, Long, LocalDateTime, LocalDateTime, Integer> fieldsRow() {
        return (Row15) super.fieldsRow();
    }

    @Override
    public Row15<Long, String, Long, String, BigDecimal, BigDecimal, BigDecimal, BigDecimal, BigDecimal, String, Long, Long, LocalDateTime, LocalDateTime, Integer> valuesRow() {
        return (Row15) super.valuesRow();
    }

    @Override
    public Field<Long> field1() {
        return EduCourse.EDU_COURSE.ID;
    }

    @Override
    public Field<String> field2() {
        return EduCourse.EDU_COURSE.NAME;
    }

    @Override
    public Field<Long> field3() {
        return EduCourse.EDU_COURSE.TYPE_ID;
    }

    @Override
    public Field<String> field4() {
        return EduCourse.EDU_COURSE.STATUS;
    }

    @Override
    public Field<BigDecimal> field5() {
        return EduCourse.EDU_COURSE.UNIT_HOURS;
    }

    @Override
    public Field<BigDecimal> field6() {
        return EduCourse.EDU_COURSE.TOTAL_HOURS;
    }

    @Override
    public Field<BigDecimal> field7() {
        return EduCourse.EDU_COURSE.CONSUMED_HOURS;
    }

    @Override
    public Field<BigDecimal> field8() {
        return EduCourse.EDU_COURSE.PRICE;
    }

    @Override
    public Field<BigDecimal> field9() {
        return EduCourse.EDU_COURSE.COACH_FEE;
    }

    @Override
    public Field<String> field10() {
        return EduCourse.EDU_COURSE.DESCRIPTION;
    }

    @Override
    public Field<Long> field11() {
        return EduCourse.EDU_COURSE.CAMPUS_ID;
    }

    @Override
    public Field<Long> field12() {
        return EduCourse.EDU_COURSE.INSTITUTION_ID;
    }

    @Override
    public Field<LocalDateTime> field13() {
        return EduCourse.EDU_COURSE.CREATED_TIME;
    }

    @Override
    public Field<LocalDateTime> field14() {
        return EduCourse.EDU_COURSE.UPDATE_TIME;
    }

    @Override
    public Field<Integer> field15() {
        return EduCourse.EDU_COURSE.DELETED;
    }

    @Override
    public Long component1() {
        return getId();
    }

    @Override
    public String component2() {
        return getName();
    }

    @Override
    public Long component3() {
        return getTypeId();
    }

    @Override
    public String component4() {
        return getStatus();
    }

    @Override
    public BigDecimal component5() {
        return getUnitHours();
    }

    @Override
    public BigDecimal component6() {
        return getTotalHours();
    }

    @Override
    public BigDecimal component7() {
        return getConsumedHours();
    }

    @Override
    public BigDecimal component8() {
        return getPrice();
    }

    @Override
    public BigDecimal component9() {
        return getCoachFee();
    }

    @Override
    public String component10() {
        return getDescription();
    }

    @Override
    public Long component11() {
        return getCampusId();
    }

    @Override
    public Long component12() {
        return getInstitutionId();
    }

    @Override
    public LocalDateTime component13() {
        return getCreatedTime();
    }

    @Override
    public LocalDateTime component14() {
        return getUpdateTime();
    }

    @Override
    public Integer component15() {
        return getDeleted();
    }

    @Override
    public Long value1() {
        return getId();
    }

    @Override
    public String value2() {
        return getName();
    }

    @Override
    public Long value3() {
        return getTypeId();
    }

    @Override
    public String value4() {
        return getStatus();
    }

    @Override
    public BigDecimal value5() {
        return getUnitHours();
    }

    @Override
    public BigDecimal value6() {
        return getTotalHours();
    }

    @Override
    public BigDecimal value7() {
        return getConsumedHours();
    }

    @Override
    public BigDecimal value8() {
        return getPrice();
    }

    @Override
    public BigDecimal value9() {
        return getCoachFee();
    }

    @Override
    public String value10() {
        return getDescription();
    }

    @Override
    public Long value11() {
        return getCampusId();
    }

    @Override
    public Long value12() {
        return getInstitutionId();
    }

    @Override
    public LocalDateTime value13() {
        return getCreatedTime();
    }

    @Override
    public LocalDateTime value14() {
        return getUpdateTime();
    }

    @Override
    public Integer value15() {
        return getDeleted();
    }

    @Override
    public EduCourseRecord value1(Long value) {
        setId(value);
        return this;
    }

    @Override
    public EduCourseRecord value2(String value) {
        setName(value);
        return this;
    }

    @Override
    public EduCourseRecord value3(Long value) {
        setTypeId(value);
        return this;
    }

    @Override
    public EduCourseRecord value4(String value) {
        setStatus(value);
        return this;
    }

    @Override
    public EduCourseRecord value5(BigDecimal value) {
        setUnitHours(value);
        return this;
    }

    @Override
    public EduCourseRecord value6(BigDecimal value) {
        setTotalHours(value);
        return this;
    }

    @Override
    public EduCourseRecord value7(BigDecimal value) {
        setConsumedHours(value);
        return this;
    }

    @Override
    public EduCourseRecord value8(BigDecimal value) {
        setPrice(value);
        return this;
    }

    @Override
    public EduCourseRecord value9(BigDecimal value) {
        setCoachFee(value);
        return this;
    }

    @Override
    public EduCourseRecord value10(String value) {
        setDescription(value);
        return this;
    }

    @Override
    public EduCourseRecord value11(Long value) {
        setCampusId(value);
        return this;
    }

    @Override
    public EduCourseRecord value12(Long value) {
        setInstitutionId(value);
        return this;
    }

    @Override
    public EduCourseRecord value13(LocalDateTime value) {
        setCreatedTime(value);
        return this;
    }

    @Override
    public EduCourseRecord value14(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public EduCourseRecord value15(Integer value) {
        setDeleted(value);
        return this;
    }

    @Override
    public EduCourseRecord values(Long value1, String value2, Long value3, String value4, BigDecimal value5, BigDecimal value6, BigDecimal value7, BigDecimal value8, BigDecimal value9, String value10, Long value11, Long value12, LocalDateTime value13, LocalDateTime value14, Integer value15) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        value15(value15);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached EduCourseRecord
     */
    public EduCourseRecord() {
        super(EduCourse.EDU_COURSE);
    }

    /**
     * Create a detached, initialised EduCourseRecord
     */
    public EduCourseRecord(Long id, String name, Long typeId, String status, BigDecimal unitHours, BigDecimal totalHours, BigDecimal consumedHours, BigDecimal price, BigDecimal coachFee, String description, Long campusId, Long institutionId, LocalDateTime createdTime, LocalDateTime updateTime, Integer deleted) {
        super(EduCourse.EDU_COURSE);

        setId(id);
        setName(name);
        setTypeId(typeId);
        setStatus(status);
        setUnitHours(unitHours);
        setTotalHours(totalHours);
        setConsumedHours(consumedHours);
        setPrice(price);
        setCoachFee(coachFee);
        setDescription(description);
        setCampusId(campusId);
        setInstitutionId(institutionId);
        setCreatedTime(createdTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
    }
}
