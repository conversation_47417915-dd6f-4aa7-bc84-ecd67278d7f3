/*
 * This file is generated by jOOQ.
 */
package com.lesson.repository.tables.records;


import com.lesson.repository.tables.SysInstitution;

import java.time.LocalDateTime;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record10;
import org.jooq.Row10;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 机构表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SysInstitutionRecord extends UpdatableRecordImpl<SysInstitutionRecord> implements Record10<Long, String, Integer, String, String, String, Integer, LocalDateTime, LocalDateTime, Integer> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lesson.sys_institution.id</code>. 主键ID
     */
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>lesson.sys_institution.id</code>. 主键ID
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>lesson.sys_institution.name</code>. 机构名称
     */
    public void setName(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>lesson.sys_institution.name</code>. 机构名称
     */
    public String getName() {
        return (String) get(1);
    }

    /**
     * Setter for <code>lesson.sys_institution.type</code>. 机构类型：1-培训机构，2-学校，3-教育集团
     */
    public void setType(Integer value) {
        set(2, value);
    }

    /**
     * Getter for <code>lesson.sys_institution.type</code>. 机构类型：1-培训机构，2-学校，3-教育集团
     */
    public Integer getType() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>lesson.sys_institution.description</code>. 机构简介
     */
    public void setDescription(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>lesson.sys_institution.description</code>. 机构简介
     */
    public String getDescription() {
        return (String) get(3);
    }

    /**
     * Setter for <code>lesson.sys_institution.manager_name</code>. 负责人姓名
     */
    public void setManagerName(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>lesson.sys_institution.manager_name</code>. 负责人姓名
     */
    public String getManagerName() {
        return (String) get(4);
    }

    /**
     * Setter for <code>lesson.sys_institution.manager_phone</code>. 负责人电话
     */
    public void setManagerPhone(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>lesson.sys_institution.manager_phone</code>. 负责人电话
     */
    public String getManagerPhone() {
        return (String) get(5);
    }

    /**
     * Setter for <code>lesson.sys_institution.status</code>. 状态：0-禁用，1-启用
     */
    public void setStatus(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>lesson.sys_institution.status</code>. 状态：0-禁用，1-启用
     */
    public Integer getStatus() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>lesson.sys_institution.created_time</code>. 创建时间
     */
    public void setCreatedTime(LocalDateTime value) {
        set(7, value);
    }

    /**
     * Getter for <code>lesson.sys_institution.created_time</code>. 创建时间
     */
    public LocalDateTime getCreatedTime() {
        return (LocalDateTime) get(7);
    }

    /**
     * Setter for <code>lesson.sys_institution.update_time</code>. 更新时间
     */
    public void setUpdateTime(LocalDateTime value) {
        set(8, value);
    }

    /**
     * Getter for <code>lesson.sys_institution.update_time</code>. 更新时间
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(8);
    }

    /**
     * Setter for <code>lesson.sys_institution.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public void setDeleted(Integer value) {
        set(9, value);
    }

    /**
     * Getter for <code>lesson.sys_institution.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public Integer getDeleted() {
        return (Integer) get(9);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record10 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row10<Long, String, Integer, String, String, String, Integer, LocalDateTime, LocalDateTime, Integer> fieldsRow() {
        return (Row10) super.fieldsRow();
    }

    @Override
    public Row10<Long, String, Integer, String, String, String, Integer, LocalDateTime, LocalDateTime, Integer> valuesRow() {
        return (Row10) super.valuesRow();
    }

    @Override
    public Field<Long> field1() {
        return SysInstitution.SYS_INSTITUTION.ID;
    }

    @Override
    public Field<String> field2() {
        return SysInstitution.SYS_INSTITUTION.NAME;
    }

    @Override
    public Field<Integer> field3() {
        return SysInstitution.SYS_INSTITUTION.TYPE;
    }

    @Override
    public Field<String> field4() {
        return SysInstitution.SYS_INSTITUTION.DESCRIPTION;
    }

    @Override
    public Field<String> field5() {
        return SysInstitution.SYS_INSTITUTION.MANAGER_NAME;
    }

    @Override
    public Field<String> field6() {
        return SysInstitution.SYS_INSTITUTION.MANAGER_PHONE;
    }

    @Override
    public Field<Integer> field7() {
        return SysInstitution.SYS_INSTITUTION.STATUS;
    }

    @Override
    public Field<LocalDateTime> field8() {
        return SysInstitution.SYS_INSTITUTION.CREATED_TIME;
    }

    @Override
    public Field<LocalDateTime> field9() {
        return SysInstitution.SYS_INSTITUTION.UPDATE_TIME;
    }

    @Override
    public Field<Integer> field10() {
        return SysInstitution.SYS_INSTITUTION.DELETED;
    }

    @Override
    public Long component1() {
        return getId();
    }

    @Override
    public String component2() {
        return getName();
    }

    @Override
    public Integer component3() {
        return getType();
    }

    @Override
    public String component4() {
        return getDescription();
    }

    @Override
    public String component5() {
        return getManagerName();
    }

    @Override
    public String component6() {
        return getManagerPhone();
    }

    @Override
    public Integer component7() {
        return getStatus();
    }

    @Override
    public LocalDateTime component8() {
        return getCreatedTime();
    }

    @Override
    public LocalDateTime component9() {
        return getUpdateTime();
    }

    @Override
    public Integer component10() {
        return getDeleted();
    }

    @Override
    public Long value1() {
        return getId();
    }

    @Override
    public String value2() {
        return getName();
    }

    @Override
    public Integer value3() {
        return getType();
    }

    @Override
    public String value4() {
        return getDescription();
    }

    @Override
    public String value5() {
        return getManagerName();
    }

    @Override
    public String value6() {
        return getManagerPhone();
    }

    @Override
    public Integer value7() {
        return getStatus();
    }

    @Override
    public LocalDateTime value8() {
        return getCreatedTime();
    }

    @Override
    public LocalDateTime value9() {
        return getUpdateTime();
    }

    @Override
    public Integer value10() {
        return getDeleted();
    }

    @Override
    public SysInstitutionRecord value1(Long value) {
        setId(value);
        return this;
    }

    @Override
    public SysInstitutionRecord value2(String value) {
        setName(value);
        return this;
    }

    @Override
    public SysInstitutionRecord value3(Integer value) {
        setType(value);
        return this;
    }

    @Override
    public SysInstitutionRecord value4(String value) {
        setDescription(value);
        return this;
    }

    @Override
    public SysInstitutionRecord value5(String value) {
        setManagerName(value);
        return this;
    }

    @Override
    public SysInstitutionRecord value6(String value) {
        setManagerPhone(value);
        return this;
    }

    @Override
    public SysInstitutionRecord value7(Integer value) {
        setStatus(value);
        return this;
    }

    @Override
    public SysInstitutionRecord value8(LocalDateTime value) {
        setCreatedTime(value);
        return this;
    }

    @Override
    public SysInstitutionRecord value9(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public SysInstitutionRecord value10(Integer value) {
        setDeleted(value);
        return this;
    }

    @Override
    public SysInstitutionRecord values(Long value1, String value2, Integer value3, String value4, String value5, String value6, Integer value7, LocalDateTime value8, LocalDateTime value9, Integer value10) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached SysInstitutionRecord
     */
    public SysInstitutionRecord() {
        super(SysInstitution.SYS_INSTITUTION);
    }

    /**
     * Create a detached, initialised SysInstitutionRecord
     */
    public SysInstitutionRecord(Long id, String name, Integer type, String description, String managerName, String managerPhone, Integer status, LocalDateTime createdTime, LocalDateTime updateTime, Integer deleted) {
        super(SysInstitution.SYS_INSTITUTION);

        setId(id);
        setName(name);
        setType(type);
        setDescription(description);
        setManagerName(managerName);
        setManagerPhone(managerPhone);
        setStatus(status);
        setCreatedTime(createdTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
    }
}
