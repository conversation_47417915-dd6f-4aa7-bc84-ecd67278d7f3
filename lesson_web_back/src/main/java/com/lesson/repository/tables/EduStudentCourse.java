/*
 * This file is generated by jOOQ.
 */
package com.lesson.repository.tables;


import com.lesson.repository.Indexes;
import com.lesson.repository.Keys;
import com.lesson.repository.Lesson;
import com.lesson.repository.tables.records.EduStudentCourseRecord;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Identity;
import org.jooq.Index;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row14;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 学员课程关系表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class EduStudentCourse extends TableImpl<EduStudentCourseRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>lesson.edu_student_course</code>
     */
    public static final EduStudentCourse EDU_STUDENT_COURSE = new EduStudentCourse();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<EduStudentCourseRecord> getRecordType() {
        return EduStudentCourseRecord.class;
    }

    /**
     * The column <code>lesson.edu_student_course.id</code>. 记录ID
     */
    public final TableField<EduStudentCourseRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "记录ID");

    /**
     * The column <code>lesson.edu_student_course.student_id</code>. 学员ID
     */
    public final TableField<EduStudentCourseRecord, Long> STUDENT_ID = createField(DSL.name("student_id"), SQLDataType.BIGINT.nullable(false), this, "学员ID");

    /**
     * The column <code>lesson.edu_student_course.course_id</code>. 课程ID
     */
    public final TableField<EduStudentCourseRecord, Long> COURSE_ID = createField(DSL.name("course_id"), SQLDataType.BIGINT.nullable(false), this, "课程ID");

    /**
     * The column <code>lesson.edu_student_course.total_hours</code>. 总课时数
     */
    public final TableField<EduStudentCourseRecord, BigDecimal> TOTAL_HOURS = createField(DSL.name("total_hours"), SQLDataType.DECIMAL(10, 2).nullable(false), this, "总课时数");

    /**
     * The column <code>lesson.edu_student_course.consumed_hours</code>. 已消耗课时数
     */
    public final TableField<EduStudentCourseRecord, BigDecimal> CONSUMED_HOURS = createField(DSL.name("consumed_hours"), SQLDataType.DECIMAL(10, 2).nullable(false).defaultValue(DSL.inline("0.00", SQLDataType.DECIMAL)), this, "已消耗课时数");

    /**
     * The column <code>lesson.edu_student_course.status</code>. 状态：STUDYING-在学，SUSPENDED-停课，GRADUATED-结业
     */
    public final TableField<EduStudentCourseRecord, String> STATUS = createField(DSL.name("status"), SQLDataType.VARCHAR(20).nullable(false), this, "状态：STUDYING-在学，SUSPENDED-停课，GRADUATED-结业");

    /**
     * The column <code>lesson.edu_student_course.start_date</code>. 报名日期
     */
    public final TableField<EduStudentCourseRecord, LocalDate> START_DATE = createField(DSL.name("start_date"), SQLDataType.LOCALDATE.nullable(false), this, "报名日期");

    /**
     * The column <code>lesson.edu_student_course.end_date</code>. 有效期至
     */
    public final TableField<EduStudentCourseRecord, LocalDate> END_DATE = createField(DSL.name("end_date"), SQLDataType.LOCALDATE, this, "有效期至");

    /**
     * The column <code>lesson.edu_student_course.fixed_schedule</code>. 固定排课时间，JSON格式
     */
    public final TableField<EduStudentCourseRecord, String> FIXED_SCHEDULE = createField(DSL.name("fixed_schedule"), SQLDataType.CLOB, this, "固定排课时间，JSON格式");

    /**
     * The column <code>lesson.edu_student_course.campus_id</code>. 校区ID
     */
    public final TableField<EduStudentCourseRecord, Long> CAMPUS_ID = createField(DSL.name("campus_id"), SQLDataType.BIGINT.nullable(false), this, "校区ID");

    /**
     * The column <code>lesson.edu_student_course.institution_id</code>. 机构ID
     */
    public final TableField<EduStudentCourseRecord, Long> INSTITUTION_ID = createField(DSL.name("institution_id"), SQLDataType.BIGINT.nullable(false), this, "机构ID");

    /**
     * The column <code>lesson.edu_student_course.created_time</code>. 创建时间
     */
    public final TableField<EduStudentCourseRecord, LocalDateTime> CREATED_TIME = createField(DSL.name("created_time"), SQLDataType.LOCALDATETIME(0).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "创建时间");

    /**
     * The column <code>lesson.edu_student_course.update_time</code>. 更新时间
     */
    public final TableField<EduStudentCourseRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(0).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "更新时间");

    /**
     * The column <code>lesson.edu_student_course.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public final TableField<EduStudentCourseRecord, Integer> DELETED = createField(DSL.name("deleted"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否删除：0-未删除，1-已删除");

    private EduStudentCourse(Name alias, Table<EduStudentCourseRecord> aliased) {
        this(alias, aliased, null);
    }

    private EduStudentCourse(Name alias, Table<EduStudentCourseRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("学员课程关系表"), TableOptions.table());
    }

    /**
     * Create an aliased <code>lesson.edu_student_course</code> table reference
     */
    public EduStudentCourse(String alias) {
        this(DSL.name(alias), EDU_STUDENT_COURSE);
    }

    /**
     * Create an aliased <code>lesson.edu_student_course</code> table reference
     */
    public EduStudentCourse(Name alias) {
        this(alias, EDU_STUDENT_COURSE);
    }

    /**
     * Create a <code>lesson.edu_student_course</code> table reference
     */
    public EduStudentCourse() {
        this(DSL.name("edu_student_course"), null);
    }

    public <O extends Record> EduStudentCourse(Table<O> child, ForeignKey<O, EduStudentCourseRecord> key) {
        super(child, key, EDU_STUDENT_COURSE);
    }

    @Override
    public Schema getSchema() {
        return Lesson.LESSON;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.<Index>asList(Indexes.EDU_STUDENT_COURSE_IDX_CAMPUS_ID, Indexes.EDU_STUDENT_COURSE_IDX_COURSE_ID, Indexes.EDU_STUDENT_COURSE_IDX_INSTITUTION_ID, Indexes.EDU_STUDENT_COURSE_IDX_STATUS, Indexes.EDU_STUDENT_COURSE_IDX_STUDENT_ID);
    }

    @Override
    public Identity<EduStudentCourseRecord, Long> getIdentity() {
        return (Identity<EduStudentCourseRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<EduStudentCourseRecord> getPrimaryKey() {
        return Keys.KEY_EDU_STUDENT_COURSE_PRIMARY;
    }

    @Override
    public List<UniqueKey<EduStudentCourseRecord>> getKeys() {
        return Arrays.<UniqueKey<EduStudentCourseRecord>>asList(Keys.KEY_EDU_STUDENT_COURSE_PRIMARY);
    }

    @Override
    public EduStudentCourse as(String alias) {
        return new EduStudentCourse(DSL.name(alias), this);
    }

    @Override
    public EduStudentCourse as(Name alias) {
        return new EduStudentCourse(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public EduStudentCourse rename(String name) {
        return new EduStudentCourse(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public EduStudentCourse rename(Name name) {
        return new EduStudentCourse(name, null);
    }

    // -------------------------------------------------------------------------
    // Row14 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row14<Long, Long, Long, BigDecimal, BigDecimal, String, LocalDate, LocalDate, String, Long, Long, LocalDateTime, LocalDateTime, Integer> fieldsRow() {
        return (Row14) super.fieldsRow();
    }
}
