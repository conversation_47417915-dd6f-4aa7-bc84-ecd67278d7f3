/*
 * This file is generated by jOOQ.
 */
package com.lesson.repository.tables;


import com.lesson.repository.Indexes;
import com.lesson.repository.Keys;
import com.lesson.repository.Lesson;
import com.lesson.repository.tables.records.EduStudentPaymentRecord;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Identity;
import org.jooq.Index;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row16;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 学员缴费记录表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class EduStudentPayment extends TableImpl<EduStudentPaymentRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>lesson.edu_student_payment</code>
     */
    public static final EduStudentPayment EDU_STUDENT_PAYMENT = new EduStudentPayment();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<EduStudentPaymentRecord> getRecordType() {
        return EduStudentPaymentRecord.class;
    }

    /**
     * The column <code>lesson.edu_student_payment.id</code>. 记录ID
     */
    public final TableField<EduStudentPaymentRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "记录ID");

    /**
     * The column <code>lesson.edu_student_payment.student_id</code>. 学员ID
     */
    public final TableField<EduStudentPaymentRecord, String> STUDENT_ID = createField(DSL.name("student_id"), SQLDataType.VARCHAR(32).nullable(false), this, "学员ID");

    /**
     * The column <code>lesson.edu_student_payment.course_id</code>. 课程ID
     */
    public final TableField<EduStudentPaymentRecord, String> COURSE_ID = createField(DSL.name("course_id"), SQLDataType.VARCHAR(32).nullable(false), this, "课程ID");

    /**
     * The column <code>lesson.edu_student_payment.payment_type</code>. 缴费类型：NEW-新报，RENEWAL-续报，TRANSFER-转课
     */
    public final TableField<EduStudentPaymentRecord, String> PAYMENT_TYPE = createField(DSL.name("payment_type"), SQLDataType.VARCHAR(50).nullable(false), this, "缴费类型：NEW-新报，RENEWAL-续报，TRANSFER-转课");

    /**
     * The column <code>lesson.edu_student_payment.amount</code>. 缴费金额
     */
    public final TableField<EduStudentPaymentRecord, BigDecimal> AMOUNT = createField(DSL.name("amount"), SQLDataType.DECIMAL(10, 2).nullable(false), this, "缴费金额");

    /**
     * The column <code>lesson.edu_student_payment.payment_method</code>. 支付方式：CASH-现金，CARD-刷卡，WECHAT-微信，ALIPAY-支付宝
     */
    public final TableField<EduStudentPaymentRecord, String> PAYMENT_METHOD = createField(DSL.name("payment_method"), SQLDataType.VARCHAR(50).nullable(false), this, "支付方式：CASH-现金，CARD-刷卡，WECHAT-微信，ALIPAY-支付宝");

    /**
     * The column <code>lesson.edu_student_payment.course_hours</code>. 课时数
     */
    public final TableField<EduStudentPaymentRecord, BigDecimal> COURSE_HOURS = createField(DSL.name("course_hours"), SQLDataType.DECIMAL(10, 2).nullable(false), this, "课时数");

    /**
     * The column <code>lesson.edu_student_payment.gift_hours</code>. 赠送课时
     */
    public final TableField<EduStudentPaymentRecord, BigDecimal> GIFT_HOURS = createField(DSL.name("gift_hours"), SQLDataType.DECIMAL(10, 2).nullable(false).defaultValue(DSL.inline("0.00", SQLDataType.DECIMAL)), this, "赠送课时");

    /**
     * The column <code>lesson.edu_student_payment.valid_until</code>. 有效期至
     */
    public final TableField<EduStudentPaymentRecord, LocalDate> VALID_UNTIL = createField(DSL.name("valid_until"), SQLDataType.LOCALDATE.nullable(false), this, "有效期至");

    /**
     * The column <code>lesson.edu_student_payment.gift_items</code>. 赠品
     */
    public final TableField<EduStudentPaymentRecord, String> GIFT_ITEMS = createField(DSL.name("gift_items"), SQLDataType.VARCHAR(500), this, "赠品");

    /**
     * The column <code>lesson.edu_student_payment.notes</code>. 备注
     */
    public final TableField<EduStudentPaymentRecord, String> NOTES = createField(DSL.name("notes"), SQLDataType.VARCHAR(500), this, "备注");

    /**
     * The column <code>lesson.edu_student_payment.campus_id</code>. 校区ID
     */
    public final TableField<EduStudentPaymentRecord, Long> CAMPUS_ID = createField(DSL.name("campus_id"), SQLDataType.BIGINT.nullable(false), this, "校区ID");

    /**
     * The column <code>lesson.edu_student_payment.institution_id</code>. 机构ID
     */
    public final TableField<EduStudentPaymentRecord, Long> INSTITUTION_ID = createField(DSL.name("institution_id"), SQLDataType.BIGINT.nullable(false), this, "机构ID");

    /**
     * The column <code>lesson.edu_student_payment.created_time</code>. 创建时间
     */
    public final TableField<EduStudentPaymentRecord, LocalDateTime> CREATED_TIME = createField(DSL.name("created_time"), SQLDataType.LOCALDATETIME(0).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "创建时间");

    /**
     * The column <code>lesson.edu_student_payment.update_time</code>. 更新时间
     */
    public final TableField<EduStudentPaymentRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(0).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "更新时间");

    /**
     * The column <code>lesson.edu_student_payment.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public final TableField<EduStudentPaymentRecord, Integer> DELETED = createField(DSL.name("deleted"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否删除：0-未删除，1-已删除");

    private EduStudentPayment(Name alias, Table<EduStudentPaymentRecord> aliased) {
        this(alias, aliased, null);
    }

    private EduStudentPayment(Name alias, Table<EduStudentPaymentRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("学员缴费记录表"), TableOptions.table());
    }

    /**
     * Create an aliased <code>lesson.edu_student_payment</code> table reference
     */
    public EduStudentPayment(String alias) {
        this(DSL.name(alias), EDU_STUDENT_PAYMENT);
    }

    /**
     * Create an aliased <code>lesson.edu_student_payment</code> table reference
     */
    public EduStudentPayment(Name alias) {
        this(alias, EDU_STUDENT_PAYMENT);
    }

    /**
     * Create a <code>lesson.edu_student_payment</code> table reference
     */
    public EduStudentPayment() {
        this(DSL.name("edu_student_payment"), null);
    }

    public <O extends Record> EduStudentPayment(Table<O> child, ForeignKey<O, EduStudentPaymentRecord> key) {
        super(child, key, EDU_STUDENT_PAYMENT);
    }

    @Override
    public Schema getSchema() {
        return Lesson.LESSON;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.<Index>asList(Indexes.EDU_STUDENT_PAYMENT_IDX_COURSE_ID, Indexes.EDU_STUDENT_PAYMENT_IDX_PAYMENT_TYPE, Indexes.EDU_STUDENT_PAYMENT_IDX_STUDENT_ID);
    }

    @Override
    public Identity<EduStudentPaymentRecord, Long> getIdentity() {
        return (Identity<EduStudentPaymentRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<EduStudentPaymentRecord> getPrimaryKey() {
        return Keys.KEY_EDU_STUDENT_PAYMENT_PRIMARY;
    }

    @Override
    public List<UniqueKey<EduStudentPaymentRecord>> getKeys() {
        return Arrays.<UniqueKey<EduStudentPaymentRecord>>asList(Keys.KEY_EDU_STUDENT_PAYMENT_PRIMARY);
    }

    @Override
    public EduStudentPayment as(String alias) {
        return new EduStudentPayment(DSL.name(alias), this);
    }

    @Override
    public EduStudentPayment as(Name alias) {
        return new EduStudentPayment(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public EduStudentPayment rename(String name) {
        return new EduStudentPayment(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public EduStudentPayment rename(Name name) {
        return new EduStudentPayment(name, null);
    }

    // -------------------------------------------------------------------------
    // Row16 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row16<Long, String, String, String, BigDecimal, String, BigDecimal, BigDecimal, LocalDate, String, String, Long, Long, LocalDateTime, LocalDateTime, Integer> fieldsRow() {
        return (Row16) super.fieldsRow();
    }
}
