/*
 * This file is generated by jOOQ.
 */
package com.lesson.repository.tables;


import com.lesson.repository.Indexes;
import com.lesson.repository.Keys;
import com.lesson.repository.Lesson;
import com.lesson.repository.tables.records.EduStudentClassTransferRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Identity;
import org.jooq.Index;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row11;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 学员转班记录表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class EduStudentClassTransfer extends TableImpl<EduStudentClassTransferRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>lesson.edu_student_class_transfer</code>
     */
    public static final EduStudentClassTransfer EDU_STUDENT_CLASS_TRANSFER = new EduStudentClassTransfer();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<EduStudentClassTransferRecord> getRecordType() {
        return EduStudentClassTransferRecord.class;
    }

    /**
     * The column <code>lesson.edu_student_class_transfer.id</code>. 记录ID
     */
    public final TableField<EduStudentClassTransferRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "记录ID");

    /**
     * The column <code>lesson.edu_student_class_transfer.student_id</code>. 学员ID
     */
    public final TableField<EduStudentClassTransferRecord, String> STUDENT_ID = createField(DSL.name("student_id"), SQLDataType.VARCHAR(32).nullable(false), this, "学员ID");

    /**
     * The column <code>lesson.edu_student_class_transfer.course_id</code>. 课程ID
     */
    public final TableField<EduStudentClassTransferRecord, String> COURSE_ID = createField(DSL.name("course_id"), SQLDataType.VARCHAR(32).nullable(false), this, "课程ID");

    /**
     * The column <code>lesson.edu_student_class_transfer.original_schedule</code>. 原上课时间
     */
    public final TableField<EduStudentClassTransferRecord, String> ORIGINAL_SCHEDULE = createField(DSL.name("original_schedule"), SQLDataType.CLOB.nullable(false), this, "原上课时间");

    /**
     * The column <code>lesson.edu_student_class_transfer.new_schedule</code>. 新上课时间
     */
    public final TableField<EduStudentClassTransferRecord, String> NEW_SCHEDULE = createField(DSL.name("new_schedule"), SQLDataType.CLOB.nullable(false), this, "新上课时间");

    /**
     * The column <code>lesson.edu_student_class_transfer.reason</code>. 转班原因
     */
    public final TableField<EduStudentClassTransferRecord, String> REASON = createField(DSL.name("reason"), SQLDataType.VARCHAR(500).nullable(false), this, "转班原因");

    /**
     * The column <code>lesson.edu_student_class_transfer.campus_id</code>. 校区ID
     */
    public final TableField<EduStudentClassTransferRecord, Long> CAMPUS_ID = createField(DSL.name("campus_id"), SQLDataType.BIGINT.nullable(false), this, "校区ID");

    /**
     * The column <code>lesson.edu_student_class_transfer.institution_id</code>. 机构ID
     */
    public final TableField<EduStudentClassTransferRecord, Long> INSTITUTION_ID = createField(DSL.name("institution_id"), SQLDataType.BIGINT.nullable(false), this, "机构ID");

    /**
     * The column <code>lesson.edu_student_class_transfer.created_time</code>. 创建时间
     */
    public final TableField<EduStudentClassTransferRecord, LocalDateTime> CREATED_TIME = createField(DSL.name("created_time"), SQLDataType.LOCALDATETIME(0).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "创建时间");

    /**
     * The column <code>lesson.edu_student_class_transfer.update_time</code>. 更新时间
     */
    public final TableField<EduStudentClassTransferRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(0).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "更新时间");

    /**
     * The column <code>lesson.edu_student_class_transfer.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public final TableField<EduStudentClassTransferRecord, Integer> DELETED = createField(DSL.name("deleted"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否删除：0-未删除，1-已删除");

    private EduStudentClassTransfer(Name alias, Table<EduStudentClassTransferRecord> aliased) {
        this(alias, aliased, null);
    }

    private EduStudentClassTransfer(Name alias, Table<EduStudentClassTransferRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("学员转班记录表"), TableOptions.table());
    }

    /**
     * Create an aliased <code>lesson.edu_student_class_transfer</code> table reference
     */
    public EduStudentClassTransfer(String alias) {
        this(DSL.name(alias), EDU_STUDENT_CLASS_TRANSFER);
    }

    /**
     * Create an aliased <code>lesson.edu_student_class_transfer</code> table reference
     */
    public EduStudentClassTransfer(Name alias) {
        this(alias, EDU_STUDENT_CLASS_TRANSFER);
    }

    /**
     * Create a <code>lesson.edu_student_class_transfer</code> table reference
     */
    public EduStudentClassTransfer() {
        this(DSL.name("edu_student_class_transfer"), null);
    }

    public <O extends Record> EduStudentClassTransfer(Table<O> child, ForeignKey<O, EduStudentClassTransferRecord> key) {
        super(child, key, EDU_STUDENT_CLASS_TRANSFER);
    }

    @Override
    public Schema getSchema() {
        return Lesson.LESSON;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.<Index>asList(Indexes.EDU_STUDENT_CLASS_TRANSFER_IDX_COURSE_ID, Indexes.EDU_STUDENT_CLASS_TRANSFER_IDX_STUDENT_ID);
    }

    @Override
    public Identity<EduStudentClassTransferRecord, Long> getIdentity() {
        return (Identity<EduStudentClassTransferRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<EduStudentClassTransferRecord> getPrimaryKey() {
        return Keys.KEY_EDU_STUDENT_CLASS_TRANSFER_PRIMARY;
    }

    @Override
    public List<UniqueKey<EduStudentClassTransferRecord>> getKeys() {
        return Arrays.<UniqueKey<EduStudentClassTransferRecord>>asList(Keys.KEY_EDU_STUDENT_CLASS_TRANSFER_PRIMARY);
    }

    @Override
    public EduStudentClassTransfer as(String alias) {
        return new EduStudentClassTransfer(DSL.name(alias), this);
    }

    @Override
    public EduStudentClassTransfer as(Name alias) {
        return new EduStudentClassTransfer(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public EduStudentClassTransfer rename(String name) {
        return new EduStudentClassTransfer(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public EduStudentClassTransfer rename(Name name) {
        return new EduStudentClassTransfer(name, null);
    }

    // -------------------------------------------------------------------------
    // Row11 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row11<Long, String, String, String, String, String, Long, Long, LocalDateTime, LocalDateTime, Integer> fieldsRow() {
        return (Row11) super.fieldsRow();
    }
}
