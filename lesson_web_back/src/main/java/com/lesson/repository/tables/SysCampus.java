/*
 * This file is generated by jOOQ.
 */
package com.lesson.repository.tables;


import com.lesson.repository.Indexes;
import com.lesson.repository.Keys;
import com.lesson.repository.Lesson;
import com.lesson.repository.tables.records.SysCampusRecord;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Identity;
import org.jooq.Index;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row11;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 校区表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SysCampus extends TableImpl<SysCampusRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>lesson.sys_campus</code>
     */
    public static final SysCampus SYS_CAMPUS = new SysCampus();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SysCampusRecord> getRecordType() {
        return SysCampusRecord.class;
    }

    /**
     * The column <code>lesson.sys_campus.id</code>. 主键ID
     */
    public final TableField<SysCampusRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "主键ID");

    /**
     * The column <code>lesson.sys_campus.institution_id</code>. 机构ID
     */
    public final TableField<SysCampusRecord, Long> INSTITUTION_ID = createField(DSL.name("institution_id"), SQLDataType.BIGINT.nullable(false).defaultValue(DSL.inline("1", SQLDataType.BIGINT)), this, "机构ID");

    /**
     * The column <code>lesson.sys_campus.name</code>. 校区名称
     */
    public final TableField<SysCampusRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(100).nullable(false), this, "校区名称");

    /**
     * The column <code>lesson.sys_campus.address</code>. 校区地址
     */
    public final TableField<SysCampusRecord, String> ADDRESS = createField(DSL.name("address"), SQLDataType.VARCHAR(255).nullable(false), this, "校区地址");

    /**
     * The column <code>lesson.sys_campus.status</code>. 状态：0-已关闭，1-营业中
     */
    public final TableField<SysCampusRecord, Integer> STATUS = createField(DSL.name("status"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("1", SQLDataType.INTEGER)), this, "状态：0-已关闭，1-营业中");

    /**
     * The column <code>lesson.sys_campus.monthly_rent</code>. 月租金
     */
    public final TableField<SysCampusRecord, BigDecimal> MONTHLY_RENT = createField(DSL.name("monthly_rent"), SQLDataType.DECIMAL(10, 2), this, "月租金");

    /**
     * The column <code>lesson.sys_campus.property_fee</code>. 物业费
     */
    public final TableField<SysCampusRecord, BigDecimal> PROPERTY_FEE = createField(DSL.name("property_fee"), SQLDataType.DECIMAL(10, 2), this, "物业费");

    /**
     * The column <code>lesson.sys_campus.utility_fee</code>. 固定水电费
     */
    public final TableField<SysCampusRecord, BigDecimal> UTILITY_FEE = createField(DSL.name("utility_fee"), SQLDataType.DECIMAL(10, 2), this, "固定水电费");

    /**
     * The column <code>lesson.sys_campus.created_time</code>. 创建时间
     */
    public final TableField<SysCampusRecord, LocalDateTime> CREATED_TIME = createField(DSL.name("created_time"), SQLDataType.LOCALDATETIME(0).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "创建时间");

    /**
     * The column <code>lesson.sys_campus.update_time</code>. 更新时间
     */
    public final TableField<SysCampusRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(0).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "更新时间");

    /**
     * The column <code>lesson.sys_campus.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public final TableField<SysCampusRecord, Integer> DELETED = createField(DSL.name("deleted"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否删除：0-未删除，1-已删除");

    private SysCampus(Name alias, Table<SysCampusRecord> aliased) {
        this(alias, aliased, null);
    }

    private SysCampus(Name alias, Table<SysCampusRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("校区表"), TableOptions.table());
    }

    /**
     * Create an aliased <code>lesson.sys_campus</code> table reference
     */
    public SysCampus(String alias) {
        this(DSL.name(alias), SYS_CAMPUS);
    }

    /**
     * Create an aliased <code>lesson.sys_campus</code> table reference
     */
    public SysCampus(Name alias) {
        this(alias, SYS_CAMPUS);
    }

    /**
     * Create a <code>lesson.sys_campus</code> table reference
     */
    public SysCampus() {
        this(DSL.name("sys_campus"), null);
    }

    public <O extends Record> SysCampus(Table<O> child, ForeignKey<O, SysCampusRecord> key) {
        super(child, key, SYS_CAMPUS);
    }

    @Override
    public Schema getSchema() {
        return Lesson.LESSON;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.<Index>asList(Indexes.SYS_CAMPUS_IDX_CREATED_TIME, Indexes.SYS_CAMPUS_IDX_INSTITUTION_ID, Indexes.SYS_CAMPUS_IDX_NAME, Indexes.SYS_CAMPUS_IDX_STATUS);
    }

    @Override
    public Identity<SysCampusRecord, Long> getIdentity() {
        return (Identity<SysCampusRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<SysCampusRecord> getPrimaryKey() {
        return Keys.KEY_SYS_CAMPUS_PRIMARY;
    }

    @Override
    public List<UniqueKey<SysCampusRecord>> getKeys() {
        return Arrays.<UniqueKey<SysCampusRecord>>asList(Keys.KEY_SYS_CAMPUS_PRIMARY);
    }

    @Override
    public SysCampus as(String alias) {
        return new SysCampus(DSL.name(alias), this);
    }

    @Override
    public SysCampus as(Name alias) {
        return new SysCampus(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public SysCampus rename(String name) {
        return new SysCampus(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public SysCampus rename(Name name) {
        return new SysCampus(name, null);
    }

    // -------------------------------------------------------------------------
    // Row11 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row11<Long, Long, String, String, Integer, BigDecimal, BigDecimal, BigDecimal, LocalDateTime, LocalDateTime, Integer> fieldsRow() {
        return (Row11) super.fieldsRow();
    }
}
