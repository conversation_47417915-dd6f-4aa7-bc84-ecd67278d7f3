/*
 * This file is generated by jOOQ.
 */
package com.lesson.repository.tables;


import com.lesson.repository.Indexes;
import com.lesson.repository.Keys;
import com.lesson.repository.Lesson;
import com.lesson.repository.tables.records.SysInstitutionRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Identity;
import org.jooq.Index;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row10;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 机构表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SysInstitution extends TableImpl<SysInstitutionRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>lesson.sys_institution</code>
     */
    public static final SysInstitution SYS_INSTITUTION = new SysInstitution();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SysInstitutionRecord> getRecordType() {
        return SysInstitutionRecord.class;
    }

    /**
     * The column <code>lesson.sys_institution.id</code>. 主键ID
     */
    public final TableField<SysInstitutionRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "主键ID");

    /**
     * The column <code>lesson.sys_institution.name</code>. 机构名称
     */
    public final TableField<SysInstitutionRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(100).nullable(false), this, "机构名称");

    /**
     * The column <code>lesson.sys_institution.type</code>. 机构类型：1-培训机构，2-学校，3-教育集团
     */
    public final TableField<SysInstitutionRecord, Integer> TYPE = createField(DSL.name("type"), SQLDataType.INTEGER.nullable(false), this, "机构类型：1-培训机构，2-学校，3-教育集团");

    /**
     * The column <code>lesson.sys_institution.description</code>. 机构简介
     */
    public final TableField<SysInstitutionRecord, String> DESCRIPTION = createField(DSL.name("description"), SQLDataType.CLOB, this, "机构简介");

    /**
     * The column <code>lesson.sys_institution.manager_name</code>. 负责人姓名
     */
    public final TableField<SysInstitutionRecord, String> MANAGER_NAME = createField(DSL.name("manager_name"), SQLDataType.VARCHAR(50).nullable(false), this, "负责人姓名");

    /**
     * The column <code>lesson.sys_institution.manager_phone</code>. 负责人电话
     */
    public final TableField<SysInstitutionRecord, String> MANAGER_PHONE = createField(DSL.name("manager_phone"), SQLDataType.VARCHAR(20).nullable(false), this, "负责人电话");

    /**
     * The column <code>lesson.sys_institution.status</code>. 状态：0-禁用，1-启用
     */
    public final TableField<SysInstitutionRecord, Integer> STATUS = createField(DSL.name("status"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("1", SQLDataType.INTEGER)), this, "状态：0-禁用，1-启用");

    /**
     * The column <code>lesson.sys_institution.created_time</code>. 创建时间
     */
    public final TableField<SysInstitutionRecord, LocalDateTime> CREATED_TIME = createField(DSL.name("created_time"), SQLDataType.LOCALDATETIME(0).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "创建时间");

    /**
     * The column <code>lesson.sys_institution.update_time</code>. 更新时间
     */
    public final TableField<SysInstitutionRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(0).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "更新时间");

    /**
     * The column <code>lesson.sys_institution.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public final TableField<SysInstitutionRecord, Integer> DELETED = createField(DSL.name("deleted"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否删除：0-未删除，1-已删除");

    private SysInstitution(Name alias, Table<SysInstitutionRecord> aliased) {
        this(alias, aliased, null);
    }

    private SysInstitution(Name alias, Table<SysInstitutionRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("机构表"), TableOptions.table());
    }

    /**
     * Create an aliased <code>lesson.sys_institution</code> table reference
     */
    public SysInstitution(String alias) {
        this(DSL.name(alias), SYS_INSTITUTION);
    }

    /**
     * Create an aliased <code>lesson.sys_institution</code> table reference
     */
    public SysInstitution(Name alias) {
        this(alias, SYS_INSTITUTION);
    }

    /**
     * Create a <code>lesson.sys_institution</code> table reference
     */
    public SysInstitution() {
        this(DSL.name("sys_institution"), null);
    }

    public <O extends Record> SysInstitution(Table<O> child, ForeignKey<O, SysInstitutionRecord> key) {
        super(child, key, SYS_INSTITUTION);
    }

    @Override
    public Schema getSchema() {
        return Lesson.LESSON;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.<Index>asList(Indexes.SYS_INSTITUTION_IDX_CREATED_TIME, Indexes.SYS_INSTITUTION_IDX_MANAGER_PHONE, Indexes.SYS_INSTITUTION_IDX_NAME, Indexes.SYS_INSTITUTION_IDX_STATUS);
    }

    @Override
    public Identity<SysInstitutionRecord, Long> getIdentity() {
        return (Identity<SysInstitutionRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<SysInstitutionRecord> getPrimaryKey() {
        return Keys.KEY_SYS_INSTITUTION_PRIMARY;
    }

    @Override
    public List<UniqueKey<SysInstitutionRecord>> getKeys() {
        return Arrays.<UniqueKey<SysInstitutionRecord>>asList(Keys.KEY_SYS_INSTITUTION_PRIMARY);
    }

    @Override
    public SysInstitution as(String alias) {
        return new SysInstitution(DSL.name(alias), this);
    }

    @Override
    public SysInstitution as(Name alias) {
        return new SysInstitution(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public SysInstitution rename(String name) {
        return new SysInstitution(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public SysInstitution rename(Name name) {
        return new SysInstitution(name, null);
    }

    // -------------------------------------------------------------------------
    // Row10 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row10<Long, String, Integer, String, String, String, Integer, LocalDateTime, LocalDateTime, Integer> fieldsRow() {
        return (Row10) super.fieldsRow();
    }
}
