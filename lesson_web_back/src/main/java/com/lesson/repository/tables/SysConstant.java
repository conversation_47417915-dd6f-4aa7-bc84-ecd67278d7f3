/*
 * This file is generated by jOOQ.
 */
package com.lesson.repository.tables;


import com.lesson.repository.Indexes;
import com.lesson.repository.Keys;
import com.lesson.repository.Lesson;
import com.lesson.repository.tables.records.SysConstantRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Identity;
import org.jooq.Index;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row9;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 系统常量表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SysConstant extends TableImpl<SysConstantRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>lesson.sys_constant</code>
     */
    public static final SysConstant SYS_CONSTANT = new SysConstant();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SysConstantRecord> getRecordType() {
        return SysConstantRecord.class;
    }

    /**
     * The column <code>lesson.sys_constant.id</code>. 主键ID
     */
    public final TableField<SysConstantRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "主键ID");

    /**
     * The column <code>lesson.sys_constant.constant_key</code>. 常量键
     */
    public final TableField<SysConstantRecord, String> CONSTANT_KEY = createField(DSL.name("constant_key"), SQLDataType.VARCHAR(100).nullable(false), this, "常量键");

    /**
     * The column <code>lesson.sys_constant.constant_value</code>. 常量值
     */
    public final TableField<SysConstantRecord, String> CONSTANT_VALUE = createField(DSL.name("constant_value"), SQLDataType.VARCHAR(255).nullable(false), this, "常量值");

    /**
     * The column <code>lesson.sys_constant.description</code>. 描述
     */
    public final TableField<SysConstantRecord, String> DESCRIPTION = createField(DSL.name("description"), SQLDataType.VARCHAR(500), this, "描述");

    /**
     * The column <code>lesson.sys_constant.type</code>. 常量类型：SYSTEM-系统常量，BUSINESS-业务常量
     */
    public final TableField<SysConstantRecord, String> TYPE = createField(DSL.name("type"), SQLDataType.VARCHAR(50).nullable(false), this, "常量类型：SYSTEM-系统常量，BUSINESS-业务常量");

    /**
     * The column <code>lesson.sys_constant.status</code>. 状态：0-禁用，1-启用
     */
    public final TableField<SysConstantRecord, Integer> STATUS = createField(DSL.name("status"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("1", SQLDataType.INTEGER)), this, "状态：0-禁用，1-启用");

    /**
     * The column <code>lesson.sys_constant.created_time</code>. 创建时间
     */
    public final TableField<SysConstantRecord, LocalDateTime> CREATED_TIME = createField(DSL.name("created_time"), SQLDataType.LOCALDATETIME(0).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "创建时间");

    /**
     * The column <code>lesson.sys_constant.update_time</code>. 更新时间
     */
    public final TableField<SysConstantRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(0).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "更新时间");

    /**
     * The column <code>lesson.sys_constant.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public final TableField<SysConstantRecord, Integer> DELETED = createField(DSL.name("deleted"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否删除：0-未删除，1-已删除");

    private SysConstant(Name alias, Table<SysConstantRecord> aliased) {
        this(alias, aliased, null);
    }

    private SysConstant(Name alias, Table<SysConstantRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("系统常量表"), TableOptions.table());
    }

    /**
     * Create an aliased <code>lesson.sys_constant</code> table reference
     */
    public SysConstant(String alias) {
        this(DSL.name(alias), SYS_CONSTANT);
    }

    /**
     * Create an aliased <code>lesson.sys_constant</code> table reference
     */
    public SysConstant(Name alias) {
        this(alias, SYS_CONSTANT);
    }

    /**
     * Create a <code>lesson.sys_constant</code> table reference
     */
    public SysConstant() {
        this(DSL.name("sys_constant"), null);
    }

    public <O extends Record> SysConstant(Table<O> child, ForeignKey<O, SysConstantRecord> key) {
        super(child, key, SYS_CONSTANT);
    }

    @Override
    public Schema getSchema() {
        return Lesson.LESSON;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.<Index>asList(Indexes.SYS_CONSTANT_IDX_CREATED_TIME, Indexes.SYS_CONSTANT_IDX_STATUS, Indexes.SYS_CONSTANT_IDX_TYPE);
    }

    @Override
    public Identity<SysConstantRecord, Long> getIdentity() {
        return (Identity<SysConstantRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<SysConstantRecord> getPrimaryKey() {
        return Keys.KEY_SYS_CONSTANT_PRIMARY;
    }

    @Override
    public List<UniqueKey<SysConstantRecord>> getKeys() {
        return Arrays.<UniqueKey<SysConstantRecord>>asList(Keys.KEY_SYS_CONSTANT_PRIMARY, Keys.KEY_SYS_CONSTANT_UK_CONSTANT_KEY);
    }

    @Override
    public SysConstant as(String alias) {
        return new SysConstant(DSL.name(alias), this);
    }

    @Override
    public SysConstant as(Name alias) {
        return new SysConstant(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public SysConstant rename(String name) {
        return new SysConstant(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public SysConstant rename(Name name) {
        return new SysConstant(name, null);
    }

    // -------------------------------------------------------------------------
    // Row9 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row9<Long, String, String, String, String, Integer, LocalDateTime, LocalDateTime, Integer> fieldsRow() {
        return (Row9) super.fieldsRow();
    }
}
