/*
 * This file is generated by jOOQ.
 */
package com.lesson.repository.tables.records;


import com.lesson.repository.tables.SysUser;

import java.time.LocalDateTime;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record13;
import org.jooq.Row13;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 系统用户表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SysUserRecord extends UpdatableRecordImpl<SysUserRecord> implements Record13<Long, String, String, String, Long, Long, Long, String, Integer, LocalDateTime, LocalDateTime, LocalDateTime, Integer> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lesson.sys_user.id</code>. 主键ID
     */
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>lesson.sys_user.id</code>. 主键ID
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>lesson.sys_user.password</code>. 密码
     */
    public void setPassword(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>lesson.sys_user.password</code>. 密码
     */
    public String getPassword() {
        return (String) get(1);
    }

    /**
     * Setter for <code>lesson.sys_user.real_name</code>. 真实姓名
     */
    public void setRealName(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>lesson.sys_user.real_name</code>. 真实姓名
     */
    public String getRealName() {
        return (String) get(2);
    }

    /**
     * Setter for <code>lesson.sys_user.phone</code>. 手机号
     */
    public void setPhone(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>lesson.sys_user.phone</code>. 手机号
     */
    public String getPhone() {
        return (String) get(3);
    }

    /**
     * Setter for <code>lesson.sys_user.role_id</code>. 角色ID
     */
    public void setRoleId(Long value) {
        set(4, value);
    }

    /**
     * Getter for <code>lesson.sys_user.role_id</code>. 角色ID
     */
    public Long getRoleId() {
        return (Long) get(4);
    }

    /**
     * Setter for <code>lesson.sys_user.institution_id</code>. 所属机构ID（超级管理员可为空或设为0）
     */
    public void setInstitutionId(Long value) {
        set(5, value);
    }

    /**
     * Getter for <code>lesson.sys_user.institution_id</code>. 所属机构ID（超级管理员可为空或设为0）
     */
    public Long getInstitutionId() {
        return (Long) get(5);
    }

    /**
     * Setter for <code>lesson.sys_user.campus_id</code>. 所属校区ID（超级管理员可为空）
     */
    public void setCampusId(Long value) {
        set(6, value);
    }

    /**
     * Getter for <code>lesson.sys_user.campus_id</code>. 所属校区ID（超级管理员可为空）
     */
    public Long getCampusId() {
        return (Long) get(6);
    }

    /**
     * Setter for <code>lesson.sys_user.avatar</code>. 头像URL
     */
    public void setAvatar(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>lesson.sys_user.avatar</code>. 头像URL
     */
    public String getAvatar() {
        return (String) get(7);
    }

    /**
     * Setter for <code>lesson.sys_user.status</code>. 状态：0-禁用，1-启用
     */
    public void setStatus(Integer value) {
        set(8, value);
    }

    /**
     * Getter for <code>lesson.sys_user.status</code>. 状态：0-禁用，1-启用
     */
    public Integer getStatus() {
        return (Integer) get(8);
    }

    /**
     * Setter for <code>lesson.sys_user.last_login_time</code>. 最后登录时间
     */
    public void setLastLoginTime(LocalDateTime value) {
        set(9, value);
    }

    /**
     * Getter for <code>lesson.sys_user.last_login_time</code>. 最后登录时间
     */
    public LocalDateTime getLastLoginTime() {
        return (LocalDateTime) get(9);
    }

    /**
     * Setter for <code>lesson.sys_user.created_time</code>. 创建时间
     */
    public void setCreatedTime(LocalDateTime value) {
        set(10, value);
    }

    /**
     * Getter for <code>lesson.sys_user.created_time</code>. 创建时间
     */
    public LocalDateTime getCreatedTime() {
        return (LocalDateTime) get(10);
    }

    /**
     * Setter for <code>lesson.sys_user.update_time</code>. 更新时间
     */
    public void setUpdateTime(LocalDateTime value) {
        set(11, value);
    }

    /**
     * Getter for <code>lesson.sys_user.update_time</code>. 更新时间
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(11);
    }

    /**
     * Setter for <code>lesson.sys_user.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public void setDeleted(Integer value) {
        set(12, value);
    }

    /**
     * Getter for <code>lesson.sys_user.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public Integer getDeleted() {
        return (Integer) get(12);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record13 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row13<Long, String, String, String, Long, Long, Long, String, Integer, LocalDateTime, LocalDateTime, LocalDateTime, Integer> fieldsRow() {
        return (Row13) super.fieldsRow();
    }

    @Override
    public Row13<Long, String, String, String, Long, Long, Long, String, Integer, LocalDateTime, LocalDateTime, LocalDateTime, Integer> valuesRow() {
        return (Row13) super.valuesRow();
    }

    @Override
    public Field<Long> field1() {
        return SysUser.SYS_USER.ID;
    }

    @Override
    public Field<String> field2() {
        return SysUser.SYS_USER.PASSWORD;
    }

    @Override
    public Field<String> field3() {
        return SysUser.SYS_USER.REAL_NAME;
    }

    @Override
    public Field<String> field4() {
        return SysUser.SYS_USER.PHONE;
    }

    @Override
    public Field<Long> field5() {
        return SysUser.SYS_USER.ROLE_ID;
    }

    @Override
    public Field<Long> field6() {
        return SysUser.SYS_USER.INSTITUTION_ID;
    }

    @Override
    public Field<Long> field7() {
        return SysUser.SYS_USER.CAMPUS_ID;
    }

    @Override
    public Field<String> field8() {
        return SysUser.SYS_USER.AVATAR;
    }

    @Override
    public Field<Integer> field9() {
        return SysUser.SYS_USER.STATUS;
    }

    @Override
    public Field<LocalDateTime> field10() {
        return SysUser.SYS_USER.LAST_LOGIN_TIME;
    }

    @Override
    public Field<LocalDateTime> field11() {
        return SysUser.SYS_USER.CREATED_TIME;
    }

    @Override
    public Field<LocalDateTime> field12() {
        return SysUser.SYS_USER.UPDATE_TIME;
    }

    @Override
    public Field<Integer> field13() {
        return SysUser.SYS_USER.DELETED;
    }

    @Override
    public Long component1() {
        return getId();
    }

    @Override
    public String component2() {
        return getPassword();
    }

    @Override
    public String component3() {
        return getRealName();
    }

    @Override
    public String component4() {
        return getPhone();
    }

    @Override
    public Long component5() {
        return getRoleId();
    }

    @Override
    public Long component6() {
        return getInstitutionId();
    }

    @Override
    public Long component7() {
        return getCampusId();
    }

    @Override
    public String component8() {
        return getAvatar();
    }

    @Override
    public Integer component9() {
        return getStatus();
    }

    @Override
    public LocalDateTime component10() {
        return getLastLoginTime();
    }

    @Override
    public LocalDateTime component11() {
        return getCreatedTime();
    }

    @Override
    public LocalDateTime component12() {
        return getUpdateTime();
    }

    @Override
    public Integer component13() {
        return getDeleted();
    }

    @Override
    public Long value1() {
        return getId();
    }

    @Override
    public String value2() {
        return getPassword();
    }

    @Override
    public String value3() {
        return getRealName();
    }

    @Override
    public String value4() {
        return getPhone();
    }

    @Override
    public Long value5() {
        return getRoleId();
    }

    @Override
    public Long value6() {
        return getInstitutionId();
    }

    @Override
    public Long value7() {
        return getCampusId();
    }

    @Override
    public String value8() {
        return getAvatar();
    }

    @Override
    public Integer value9() {
        return getStatus();
    }

    @Override
    public LocalDateTime value10() {
        return getLastLoginTime();
    }

    @Override
    public LocalDateTime value11() {
        return getCreatedTime();
    }

    @Override
    public LocalDateTime value12() {
        return getUpdateTime();
    }

    @Override
    public Integer value13() {
        return getDeleted();
    }

    @Override
    public SysUserRecord value1(Long value) {
        setId(value);
        return this;
    }

    @Override
    public SysUserRecord value2(String value) {
        setPassword(value);
        return this;
    }

    @Override
    public SysUserRecord value3(String value) {
        setRealName(value);
        return this;
    }

    @Override
    public SysUserRecord value4(String value) {
        setPhone(value);
        return this;
    }

    @Override
    public SysUserRecord value5(Long value) {
        setRoleId(value);
        return this;
    }

    @Override
    public SysUserRecord value6(Long value) {
        setInstitutionId(value);
        return this;
    }

    @Override
    public SysUserRecord value7(Long value) {
        setCampusId(value);
        return this;
    }

    @Override
    public SysUserRecord value8(String value) {
        setAvatar(value);
        return this;
    }

    @Override
    public SysUserRecord value9(Integer value) {
        setStatus(value);
        return this;
    }

    @Override
    public SysUserRecord value10(LocalDateTime value) {
        setLastLoginTime(value);
        return this;
    }

    @Override
    public SysUserRecord value11(LocalDateTime value) {
        setCreatedTime(value);
        return this;
    }

    @Override
    public SysUserRecord value12(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public SysUserRecord value13(Integer value) {
        setDeleted(value);
        return this;
    }

    @Override
    public SysUserRecord values(Long value1, String value2, String value3, String value4, Long value5, Long value6, Long value7, String value8, Integer value9, LocalDateTime value10, LocalDateTime value11, LocalDateTime value12, Integer value13) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached SysUserRecord
     */
    public SysUserRecord() {
        super(SysUser.SYS_USER);
    }

    /**
     * Create a detached, initialised SysUserRecord
     */
    public SysUserRecord(Long id, String password, String realName, String phone, Long roleId, Long institutionId, Long campusId, String avatar, Integer status, LocalDateTime lastLoginTime, LocalDateTime createdTime, LocalDateTime updateTime, Integer deleted) {
        super(SysUser.SYS_USER);

        setId(id);
        setPassword(password);
        setRealName(realName);
        setPhone(phone);
        setRoleId(roleId);
        setInstitutionId(institutionId);
        setCampusId(campusId);
        setAvatar(avatar);
        setStatus(status);
        setLastLoginTime(lastLoginTime);
        setCreatedTime(createdTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
    }
}
