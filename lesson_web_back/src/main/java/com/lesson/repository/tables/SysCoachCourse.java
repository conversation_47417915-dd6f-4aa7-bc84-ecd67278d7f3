/*
 * This file is generated by jOOQ.
 */
package com.lesson.repository.tables;


import com.lesson.repository.Indexes;
import com.lesson.repository.Keys;
import com.lesson.repository.Lesson;
import com.lesson.repository.tables.records.SysCoachCourseRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Identity;
import org.jooq.Index;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row6;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 教练课程关联表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SysCoachCourse extends TableImpl<SysCoachCourseRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>lesson.sys_coach_course</code>
     */
    public static final SysCoachCourse SYS_COACH_COURSE = new SysCoachCourse();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SysCoachCourseRecord> getRecordType() {
        return SysCoachCourseRecord.class;
    }

    /**
     * The column <code>lesson.sys_coach_course.id</code>. 主键ID
     */
    public final TableField<SysCoachCourseRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "主键ID");

    /**
     * The column <code>lesson.sys_coach_course.coach_id</code>. 关联教练ID
     */
    public final TableField<SysCoachCourseRecord, Long> COACH_ID = createField(DSL.name("coach_id"), SQLDataType.BIGINT.nullable(false), this, "关联教练ID");

    /**
     * The column <code>lesson.sys_coach_course.course_id</code>. 关联课程ID
     */
    public final TableField<SysCoachCourseRecord, Long> COURSE_ID = createField(DSL.name("course_id"), SQLDataType.BIGINT.nullable(false), this, "关联课程ID");

    /**
     * The column <code>lesson.sys_coach_course.created_time</code>. 创建时间
     */
    public final TableField<SysCoachCourseRecord, LocalDateTime> CREATED_TIME = createField(DSL.name("created_time"), SQLDataType.LOCALDATETIME(0).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "创建时间");

    /**
     * The column <code>lesson.sys_coach_course.update_time</code>. 更新时间
     */
    public final TableField<SysCoachCourseRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(0).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "更新时间");

    /**
     * The column <code>lesson.sys_coach_course.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public final TableField<SysCoachCourseRecord, Integer> DELETED = createField(DSL.name("deleted"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否删除：0-未删除，1-已删除");

    private SysCoachCourse(Name alias, Table<SysCoachCourseRecord> aliased) {
        this(alias, aliased, null);
    }

    private SysCoachCourse(Name alias, Table<SysCoachCourseRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("教练课程关联表"), TableOptions.table());
    }

    /**
     * Create an aliased <code>lesson.sys_coach_course</code> table reference
     */
    public SysCoachCourse(String alias) {
        this(DSL.name(alias), SYS_COACH_COURSE);
    }

    /**
     * Create an aliased <code>lesson.sys_coach_course</code> table reference
     */
    public SysCoachCourse(Name alias) {
        this(alias, SYS_COACH_COURSE);
    }

    /**
     * Create a <code>lesson.sys_coach_course</code> table reference
     */
    public SysCoachCourse() {
        this(DSL.name("sys_coach_course"), null);
    }

    public <O extends Record> SysCoachCourse(Table<O> child, ForeignKey<O, SysCoachCourseRecord> key) {
        super(child, key, SYS_COACH_COURSE);
    }

    @Override
    public Schema getSchema() {
        return Lesson.LESSON;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.<Index>asList(Indexes.SYS_COACH_COURSE_IDX_COACH_ID, Indexes.SYS_COACH_COURSE_IDX_COURSE_ID);
    }

    @Override
    public Identity<SysCoachCourseRecord, Long> getIdentity() {
        return (Identity<SysCoachCourseRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<SysCoachCourseRecord> getPrimaryKey() {
        return Keys.KEY_SYS_COACH_COURSE_PRIMARY;
    }

    @Override
    public List<UniqueKey<SysCoachCourseRecord>> getKeys() {
        return Arrays.<UniqueKey<SysCoachCourseRecord>>asList(Keys.KEY_SYS_COACH_COURSE_PRIMARY, Keys.KEY_SYS_COACH_COURSE_UK_COACH_COURSE);
    }

    @Override
    public SysCoachCourse as(String alias) {
        return new SysCoachCourse(DSL.name(alias), this);
    }

    @Override
    public SysCoachCourse as(Name alias) {
        return new SysCoachCourse(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public SysCoachCourse rename(String name) {
        return new SysCoachCourse(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public SysCoachCourse rename(Name name) {
        return new SysCoachCourse(name, null);
    }

    // -------------------------------------------------------------------------
    // Row6 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row6<Long, Long, Long, LocalDateTime, LocalDateTime, Integer> fieldsRow() {
        return (Row6) super.fieldsRow();
    }
}
