/*
 * This file is generated by jOOQ.
 */
package com.lesson.repository.tables.records;


import com.lesson.repository.tables.SysRole;

import java.time.LocalDateTime;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record7;
import org.jooq.Row7;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 系统角色表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SysRoleRecord extends UpdatableRecordImpl<SysRoleRecord> implements Record7<Long, String, String, Integer, LocalDateTime, LocalDateTime, Integer> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lesson.sys_role.id</code>. 主键ID
     */
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>lesson.sys_role.id</code>. 主键ID
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>lesson.sys_role.role_name</code>. 角色名称
     */
    public void setRoleName(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>lesson.sys_role.role_name</code>. 角色名称
     */
    public String getRoleName() {
        return (String) get(1);
    }

    /**
     * Setter for <code>lesson.sys_role.description</code>. 角色描述
     */
    public void setDescription(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>lesson.sys_role.description</code>. 角色描述
     */
    public String getDescription() {
        return (String) get(2);
    }

    /**
     * Setter for <code>lesson.sys_role.status</code>. 状态：0-禁用，1-启用
     */
    public void setStatus(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>lesson.sys_role.status</code>. 状态：0-禁用，1-启用
     */
    public Integer getStatus() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>lesson.sys_role.created_time</code>. 创建时间
     */
    public void setCreatedTime(LocalDateTime value) {
        set(4, value);
    }

    /**
     * Getter for <code>lesson.sys_role.created_time</code>. 创建时间
     */
    public LocalDateTime getCreatedTime() {
        return (LocalDateTime) get(4);
    }

    /**
     * Setter for <code>lesson.sys_role.update_time</code>. 更新时间
     */
    public void setUpdateTime(LocalDateTime value) {
        set(5, value);
    }

    /**
     * Getter for <code>lesson.sys_role.update_time</code>. 更新时间
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(5);
    }

    /**
     * Setter for <code>lesson.sys_role.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public void setDeleted(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>lesson.sys_role.deleted</code>. 是否删除：0-未删除，1-已删除
     */
    public Integer getDeleted() {
        return (Integer) get(6);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record7 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row7<Long, String, String, Integer, LocalDateTime, LocalDateTime, Integer> fieldsRow() {
        return (Row7) super.fieldsRow();
    }

    @Override
    public Row7<Long, String, String, Integer, LocalDateTime, LocalDateTime, Integer> valuesRow() {
        return (Row7) super.valuesRow();
    }

    @Override
    public Field<Long> field1() {
        return SysRole.SYS_ROLE.ID;
    }

    @Override
    public Field<String> field2() {
        return SysRole.SYS_ROLE.ROLE_NAME;
    }

    @Override
    public Field<String> field3() {
        return SysRole.SYS_ROLE.DESCRIPTION;
    }

    @Override
    public Field<Integer> field4() {
        return SysRole.SYS_ROLE.STATUS;
    }

    @Override
    public Field<LocalDateTime> field5() {
        return SysRole.SYS_ROLE.CREATED_TIME;
    }

    @Override
    public Field<LocalDateTime> field6() {
        return SysRole.SYS_ROLE.UPDATE_TIME;
    }

    @Override
    public Field<Integer> field7() {
        return SysRole.SYS_ROLE.DELETED;
    }

    @Override
    public Long component1() {
        return getId();
    }

    @Override
    public String component2() {
        return getRoleName();
    }

    @Override
    public String component3() {
        return getDescription();
    }

    @Override
    public Integer component4() {
        return getStatus();
    }

    @Override
    public LocalDateTime component5() {
        return getCreatedTime();
    }

    @Override
    public LocalDateTime component6() {
        return getUpdateTime();
    }

    @Override
    public Integer component7() {
        return getDeleted();
    }

    @Override
    public Long value1() {
        return getId();
    }

    @Override
    public String value2() {
        return getRoleName();
    }

    @Override
    public String value3() {
        return getDescription();
    }

    @Override
    public Integer value4() {
        return getStatus();
    }

    @Override
    public LocalDateTime value5() {
        return getCreatedTime();
    }

    @Override
    public LocalDateTime value6() {
        return getUpdateTime();
    }

    @Override
    public Integer value7() {
        return getDeleted();
    }

    @Override
    public SysRoleRecord value1(Long value) {
        setId(value);
        return this;
    }

    @Override
    public SysRoleRecord value2(String value) {
        setRoleName(value);
        return this;
    }

    @Override
    public SysRoleRecord value3(String value) {
        setDescription(value);
        return this;
    }

    @Override
    public SysRoleRecord value4(Integer value) {
        setStatus(value);
        return this;
    }

    @Override
    public SysRoleRecord value5(LocalDateTime value) {
        setCreatedTime(value);
        return this;
    }

    @Override
    public SysRoleRecord value6(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public SysRoleRecord value7(Integer value) {
        setDeleted(value);
        return this;
    }

    @Override
    public SysRoleRecord values(Long value1, String value2, String value3, Integer value4, LocalDateTime value5, LocalDateTime value6, Integer value7) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached SysRoleRecord
     */
    public SysRoleRecord() {
        super(SysRole.SYS_ROLE);
    }

    /**
     * Create a detached, initialised SysRoleRecord
     */
    public SysRoleRecord(Long id, String roleName, String description, Integer status, LocalDateTime createdTime, LocalDateTime updateTime, Integer deleted) {
        super(SysRole.SYS_ROLE);

        setId(id);
        setRoleName(roleName);
        setDescription(description);
        setStatus(status);
        setCreatedTime(createdTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
    }
}
