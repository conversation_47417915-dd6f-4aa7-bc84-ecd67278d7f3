/*
 * This file is generated by jOOQ.
 */
package com.lesson.repository;


import com.lesson.repository.tables.EduCourse;
import com.lesson.repository.tables.EduCourseRecord;
import com.lesson.repository.tables.EduStudent;
import com.lesson.repository.tables.EduStudentClassTransfer;
import com.lesson.repository.tables.EduStudentCourse;
import com.lesson.repository.tables.EduStudentCourseOperation;
import com.lesson.repository.tables.EduStudentCourseRecord;
import com.lesson.repository.tables.EduStudentCourseTransfer;
import com.lesson.repository.tables.EduStudentPayment;
import com.lesson.repository.tables.EduStudentRefund;
import com.lesson.repository.tables.SysCampus;
import com.lesson.repository.tables.SysCoach;
import com.lesson.repository.tables.SysCoachCertification;
import com.lesson.repository.tables.SysCoachCourse;
import com.lesson.repository.tables.SysCoachSalary;
import com.lesson.repository.tables.SysConstant;
import com.lesson.repository.tables.SysInstitution;
import com.lesson.repository.tables.SysRole;
import com.lesson.repository.tables.SysRolePermission;
import com.lesson.repository.tables.SysUser;

import java.util.Arrays;
import java.util.List;

import org.jooq.Catalog;
import org.jooq.Table;
import org.jooq.impl.SchemaImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Lesson extends SchemaImpl {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>lesson</code>
     */
    public static final Lesson LESSON = new Lesson();

    /**
     * 课程表
     */
    public final EduCourse EDU_COURSE = EduCourse.EDU_COURSE;

    /**
     * 课程上课记录表
     */
    public final EduCourseRecord EDU_COURSE_RECORD = EduCourseRecord.EDU_COURSE_RECORD;

    /**
     * 学员表
     */
    public final EduStudent EDU_STUDENT = EduStudent.EDU_STUDENT;

    /**
     * 学员转班记录表
     */
    public final EduStudentClassTransfer EDU_STUDENT_CLASS_TRANSFER = EduStudentClassTransfer.EDU_STUDENT_CLASS_TRANSFER;

    /**
     * 学员课程关系表
     */
    public final EduStudentCourse EDU_STUDENT_COURSE = EduStudentCourse.EDU_STUDENT_COURSE;

    /**
     * 学员课程操作记录表
     */
    public final EduStudentCourseOperation EDU_STUDENT_COURSE_OPERATION = EduStudentCourseOperation.EDU_STUDENT_COURSE_OPERATION;

    /**
     * 学员课程记录表
     */
    public final EduStudentCourseRecord EDU_STUDENT_COURSE_RECORD = EduStudentCourseRecord.EDU_STUDENT_COURSE_RECORD;

    /**
     * 学员转课记录表
     */
    public final EduStudentCourseTransfer EDU_STUDENT_COURSE_TRANSFER = EduStudentCourseTransfer.EDU_STUDENT_COURSE_TRANSFER;

    /**
     * 学员缴费记录表
     */
    public final EduStudentPayment EDU_STUDENT_PAYMENT = EduStudentPayment.EDU_STUDENT_PAYMENT;

    /**
     * 学员退费记录表
     */
    public final EduStudentRefund EDU_STUDENT_REFUND = EduStudentRefund.EDU_STUDENT_REFUND;

    /**
     * 校区表
     */
    public final SysCampus SYS_CAMPUS = SysCampus.SYS_CAMPUS;

    /**
     * 教练表
     */
    public final SysCoach SYS_COACH = SysCoach.SYS_COACH;

    /**
     * 教练证书表
     */
    public final SysCoachCertification SYS_COACH_CERTIFICATION = SysCoachCertification.SYS_COACH_CERTIFICATION;

    /**
     * 教练课程关联表
     */
    public final SysCoachCourse SYS_COACH_COURSE = SysCoachCourse.SYS_COACH_COURSE;

    /**
     * 教练薪资表
     */
    public final SysCoachSalary SYS_COACH_SALARY = SysCoachSalary.SYS_COACH_SALARY;

    /**
     * 系统常量表
     */
    public final SysConstant SYS_CONSTANT = SysConstant.SYS_CONSTANT;

    /**
     * 机构表
     */
    public final SysInstitution SYS_INSTITUTION = SysInstitution.SYS_INSTITUTION;

    /**
     * 系统角色表
     */
    public final SysRole SYS_ROLE = SysRole.SYS_ROLE;

    /**
     * 角色权限关联表
     */
    public final SysRolePermission SYS_ROLE_PERMISSION = SysRolePermission.SYS_ROLE_PERMISSION;

    /**
     * 系统用户表
     */
    public final SysUser SYS_USER = SysUser.SYS_USER;

    /**
     * No further instances allowed
     */
    private Lesson() {
        super("lesson", null);
    }


    @Override
    public Catalog getCatalog() {
        return DefaultCatalog.DEFAULT_CATALOG;
    }

    @Override
    public final List<Table<?>> getTables() {
        return Arrays.<Table<?>>asList(
            EduCourse.EDU_COURSE,
            EduCourseRecord.EDU_COURSE_RECORD,
            EduStudent.EDU_STUDENT,
            EduStudentClassTransfer.EDU_STUDENT_CLASS_TRANSFER,
            EduStudentCourse.EDU_STUDENT_COURSE,
            EduStudentCourseOperation.EDU_STUDENT_COURSE_OPERATION,
            EduStudentCourseRecord.EDU_STUDENT_COURSE_RECORD,
            EduStudentCourseTransfer.EDU_STUDENT_COURSE_TRANSFER,
            EduStudentPayment.EDU_STUDENT_PAYMENT,
            EduStudentRefund.EDU_STUDENT_REFUND,
            SysCampus.SYS_CAMPUS,
            SysCoach.SYS_COACH,
            SysCoachCertification.SYS_COACH_CERTIFICATION,
            SysCoachCourse.SYS_COACH_COURSE,
            SysCoachSalary.SYS_COACH_SALARY,
            SysConstant.SYS_CONSTANT,
            SysInstitution.SYS_INSTITUTION,
            SysRole.SYS_ROLE,
            SysRolePermission.SYS_ROLE_PERMISSION,
            SysUser.SYS_USER);
    }
}
