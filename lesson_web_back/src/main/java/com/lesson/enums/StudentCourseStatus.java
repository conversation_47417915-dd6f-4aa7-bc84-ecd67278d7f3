package com.lesson.enums;

/**
 * 学员课程状态枚举
 */
public enum StudentCourseStatus {
    STUDYING(1, "studying", "学习中"),
    EXPIRED(2, "expired", "过期"),
    GRADUATED(3, "graduated", "结业"),
    WAITING_PAYMENT(4, "waiting_payment", "待缴费"),
    WAITING_CLASS(5, "waiting_class", "待上课"),
    WAITING_RENEWAL(6, "waiting_renewal", "待续费"),
    REFUNDED(7, "refunded", "已退费");

    private final Integer code;
    private final String name;
    private final String desc;

    StudentCourseStatus(Integer code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static StudentCourseStatus getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (StudentCourseStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    public static StudentCourseStatus getByName(String name) {
        if (name == null) {
            return null;
        }
        for (StudentCourseStatus status : values()) {
            if (status.getName().equals(name)) {
                return status;
            }
        }
        return null;
    }
}
