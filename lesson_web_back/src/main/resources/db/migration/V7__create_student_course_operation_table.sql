-- 创建学员课程操作记录表
CREATE TABLE edu_student_course_operation (
    id bigint(20) AUTO_INCREMENT PRIMARY KEY COMMENT '记录ID',
    student_id bigint(20) NOT NULL COMMENT '学员ID',
    student_name VARCHAR(50) NOT NULL COMMENT '学员姓名',
    course_id bigint(20) NOT NULL COMMENT '课程ID',
    operation_type VARCHAR(20) NOT NULL COMMENT '操作类型：TRANSFER_COURSE-转课，TRANSFER_CLASS-转班，REFUND-退费',
    before_status VARCHAR(20) NOT NULL COMMENT '操作前状态',
    after_status VARCHAR(20) NOT NULL COMMENT '操作后状态',
    source_course_id bigint(20) COMMENT '原课程ID',
    target_course_id bigint(20) COMMENT '目标课程ID',
    source_class_id bigint(20) COMMENT '原班级ID',
    source_class_name VARCHAR(100) COMMENT '原班级名称',
    target_class_id bigint(20) COMMENT '目标班级ID',
    target_class_name VARCHAR(100) COMMENT '目标班级名称',
    refund_amount DECIMAL(10,2) COMMENT '退费金额',
    refund_method VARCHAR(20) COMMENT '退费方式：CASH-现金，BANK_TRANSFER-银行转账，WECHAT-微信，ALIPAY-支付宝',
    operation_reason VARCHAR(500) COMMENT '操作原因',
    operator_id bigint(20) NOT NULL COMMENT '操作人ID',
    operator_name VARCHAR(50) NOT NULL COMMENT '操作人姓名',
    operation_time DATETIME NOT NULL COMMENT '操作时间',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    INDEX idx_student_id (student_id),
    INDEX idx_course_id (course_id),
    INDEX idx_operation_type (operation_type),
    INDEX idx_operation_time (operation_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学员课程操作记录表'; 