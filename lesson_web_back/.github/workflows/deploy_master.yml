name: deploy_master_web_back
on:
  push:
    branches:
      - master

jobs:
  master-lesson-web-back:
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: JDK 8
        uses: actions/setup-java@v4
        with:
          distribution: 'zulu'
          java-version: 8
      - name: Cache Maven packages
        uses: actions/cache@v3
        with:
          path: ~/.m2
          key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
          restore-keys: ${{ runner.os }}-m2
      - name: Flyway Clean
        if: ${{ vars.FLYWAY_CLEAN == '1' }}
        run: mvn flyway:clean -P prod
      - name: Build application lesson_web_back
        run: mvn clean package -Pprod -DskipTests
      - name: Verify jar file exists
        run: ls -la target/lesson_web_back-prod.jar
      - name: Scp jar To HW Server
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.HW_TEST_SERVER }}
          username: ${{ secrets.HW_SERVER_USERNAME }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          port: 22
          source: ${{ github.workspace }}/target/lesson_web_back-prod.jar
          target: /root/lesson
          strip_components: ${{ secrets.STRIP_COMPONENTS }}
      - name: Run Java Service Of lesson-web-back-prod on HW Server
        uses: fifsky/ssh-action@master
        with:
          command: supervisorctl status lesson-web-back-prod | awk '{print $2}' | while read line; do if [ $line == 'RUNNING' ]; then supervisorctl restart lesson-web-back-prod;else supervisorctl start lesson-web-back-prod; fi done
          host: ${{ secrets.HW_TEST_SERVER }}
          user: ${{ secrets.HW_SERVER_USERNAME }}
          pass: ${{ secrets.HW_SERVER_PWD }}
          args: "-tt"
