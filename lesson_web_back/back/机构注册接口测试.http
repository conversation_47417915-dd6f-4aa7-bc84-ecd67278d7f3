### 机构注册验证接口测试

# 1. 机构注册测试
POST http://localhost:8080/lesson/api/institution/register
Content-Type: application/json

{
    "name": "北京朝阳校区（已更新）",
    "contactName": "朝阳校区管理员",
    "username": "chaoyang_admin",
    "password": "admin123456"
}

###

# 2. 机构详情查询测试
GET http://localhost:8080/lesson/api/institution/detail?id=1

###

# 3. 超级管理员登录测试
POST http://localhost:8080/lesson/api/auth/login
Content-Type: application/json

{
    "phone": "chaoyang_admin",
    "password": "admin123456"
}

###

# 4. 重复注册测试（应该失败）
POST http://localhost:8080/lesson/api/institution/register
Content-Type: application/json

{
    "name": "重复注册测试",
    "contactName": "测试管理员",
    "username": "chaoyang_admin",
    "password": "test123456"
}

###

# 5. 参数验证测试（应该失败）
POST http://localhost:8080/lesson/api/institution/register
Content-Type: application/json

{
    "name": "",
    "contactName": "",
    "username": "",
    "password": ""
}

###

# 6. 使用超级管理员token测试权限
# 先登录获取token
POST http://localhost:8080/lesson/api/auth/login
Content-Type: application/json

{
    "phone": "chaoyang_admin",
    "password": "admin123456"
}

###

# 使用获取到的token测试用户列表接口
GET http://localhost:8080/lesson/api/user/list
Authorization: Bearer {{login.response.body.data.token}}

### 