#!/usr/bin/env python3
import argparse
import json
import sys
from typing import Any, Dict, Iterator, Optional

try:
    import requests  # type: ignore
except Exception:
    requests = None  # lazy import check in runtime

DEFAULT_INPUT = "/Users/<USER>/program/lesson/create_students_payload.from_x.ndjson"
DEFAULT_HOST = "http://localhost:3000"
DEFAULT_ENDPOINT = "/lesson/api/student/create"
DEFAULT_OUT_LOG = "/Users/<USER>/program/lesson/create_students_payload.call.result.ndjson"


def read_ndjson(path: str) -> Iterator[Dict[str, Any]]:
    with open(path, "r", encoding="utf-8") as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
            try:
                yield json.loads(line)
            except json.JSONDecodeError as e:
                raise RuntimeError(f"Invalid JSON line in {path}: {e}\nLine: {line[:200]}")


def build_headers(token: str) -> Dict[str, str]:
    return {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json",
        "Accept": "application/json",
    }


def post_one(session: "requests.Session", url: str, headers: Dict[str, str], payload: Dict[str, Any]) -> Dict[str, Any]:
    resp = session.post(url, headers=headers, data=json.dumps(payload), timeout=15)
    try:
        data = resp.json()
    except Exception:
        data = {"text": resp.text}
    return {"status": resp.status_code, "data": data}


def main() -> None:
    parser = argparse.ArgumentParser(description="Call create student API using NDJSON payloads")
    parser.add_argument("--host", default=DEFAULT_HOST, help="API host, e.g. http://localhost:3000")
    parser.add_argument("--token", required=True, help="JWT token for Authorization header")
    parser.add_argument("--input", default=DEFAULT_INPUT, help="NDJSON payload file path")
    parser.add_argument("--campus-id", type=int, default=1, help="Campus ID to inject into studentInfo.campusId")
    parser.add_argument("--start", type=int, default=0, help="Start index (0-based)")
    parser.add_argument("--limit", type=int, default=0, help="Max records to send (0 means all)")
    parser.add_argument("--out", default=DEFAULT_OUT_LOG, help="Output result NDJSON log file")
    parser.add_argument("--dry-run", action="store_true", default=True, help="Dry run (do not send requests)")
    parser.add_argument("--execute", action="store_true", help="Actually send requests (overrides --dry-run)")

    args = parser.parse_args()

    # Safety: default is dry-run unless --execute provided
    do_execute = bool(args.execute)

    # Lazy import check
    if do_execute and requests is None:
        print("requests library not installed. Install with: pip install requests", file=sys.stderr)
        sys.exit(2)

    url = args.host.rstrip("/") + DEFAULT_ENDPOINT
    print(f"Using host: {args.host}")
    print(f"Endpoint: {url}")
    print(f"Input: {args.input}")
    print(f"CampusId: {args.campus_id}")
    print(f"Mode: {'EXECUTE' if do_execute else 'DRY-RUN'}")
    print(f"Log will be written to: {args.out}")

    headers = build_headers(args.token)

    sent = 0
    total = 0

    session: Optional["requests.Session"] = None
    if do_execute:
        session = requests.Session()  # type: ignore

    with open(args.out, "w", encoding="utf-8") as out_log:
        for idx, payload in enumerate(read_ndjson(args.input)):
            total += 1
            if idx < args.start:
                continue
            if args.limit and sent >= args.limit:
                break

            # Validate structure
            if not isinstance(payload, dict) or "studentInfo" not in payload or "courseInfoList" not in payload:
                record = {
                    "index": idx,
                    "error": "invalid_payload_structure",
                }
                out_log.write(json.dumps(record, ensure_ascii=False) + "\n")
                continue

            # Inject campusId
            payload["studentInfo"] = dict(payload["studentInfo"])  # shallow copy
            payload["studentInfo"]["campusId"] = int(args.campus_id)

            # Optional: ensure phone is string 11 digits (payload生成已保证)
            phone = payload["studentInfo"].get("phone", "")
            if isinstance(phone, str):
                digits = ''.join(ch for ch in phone if ch.isdigit())
                if digits and len(digits) >= 11:
                    payload["studentInfo"]["phone"] = digits[:11]

            record: Dict[str, Any] = {"index": idx, "name": payload["studentInfo"].get("name")}

            if do_execute and session is not None:
                try:
                    res = post_one(session, url, headers, payload)
                    record.update({
                        "status": res.get("status"),
                        "response": res.get("data"),
                    })
                except Exception as e:
                    record.update({"error": str(e)})
            else:
                # Dry run: preview a compact payload subset
                preview = {
                    "studentInfo": {
                        k: payload["studentInfo"].get(k)
                        for k in ("name", "gender", "phone", "sourceId", "campusId")
                    },
                    "courseInfoList": [
                        {k: payload["courseInfoList"][0].get(k) for k in ("courseId", "startDate")}
                    ],
                }
                record.update({"dryRunPreview": preview})

            out_log.write(json.dumps(record, ensure_ascii=False) + "\n")
            sent += 1

    print(f"Prepared {sent} requests (from {total} lines). See log: {args.out}")


if __name__ == "__main__":
    main() 