import csv
import hashlib
import os
from typing import Dict, List, Tuple

FEE_CSV_PATH = "/Users/<USER>/program/lesson/_fee.csv"
STU_CSV_PATH = "/Users/<USER>/program/lesson/_student.csv"
OUT_CSV_PATH = "/Users/<USER>/program/lesson/x.csv"

NAME_CANDIDATES_STUDENT: List[str] = [
    "姓名", "学员姓名", "学生姓名", "学生", "学员", "名字", "名称"
]
PHONE_CANDIDATES_STUDENT: List[str] = [
    "手机号码", "手机号", "手机", "电话", "联系电话", "联系方式", "家长电话", "家长手机号"
]

NAME_CANDIDATES_FEE: List[str] = [
    "姓名", "学员姓名", "学生姓名", "学生", "学员", "名字", "名称", "宝宝姓名"
]
PHONE_CANDIDATES_FEE: List[str] = [
    "家长电话", "电话", "联系电话", "手机", "手机号码", "联系方式"
]


def normalize_header(header: str) -> str:
    return (header or "").strip()


def pick_header(fieldnames: List[str], candidates: List[str]) -> str:
    normalized = {normalize_header(h): h for h in fieldnames}
    # exact match by candidate order
    for cand in candidates:
        cand_norm = normalize_header(cand)
        if cand_norm in normalized and normalized[cand_norm] != "":
            return normalized[cand_norm]
    # fallback: contains
    for cand in candidates:
        for fn in fieldnames:
            if fn and cand in fn:
                return fn
    raise KeyError(f"No matching header found in {fieldnames} for candidates {candidates}")


def generate_virtual_phone(name: str) -> str:
    digest = hashlib.md5((name or "").encode("utf-8")).hexdigest()
    suffix_num = int(digest[:8], 16) % 90000000 + 10000000  # 8 digits
    prefixes = [
        "139", "138", "137", "136", "135", "159", "158", "157",
        "178", "188", "187", "186", "185", "184", "183", "182",
        "181", "180", "173", "172", "171", "170", "199"
    ]
    prefix = prefixes[int(digest[8:10], 16) % len(prefixes)]
    return f"{prefix}{suffix_num:08d}"


def build_student_phone_map() -> Dict[str, str]:
    with open(STU_CSV_PATH, "r", encoding="utf-8-sig", newline="") as f:
        reader = csv.DictReader(f)
        fieldnames = reader.fieldnames or []
        name_key = pick_header(fieldnames, NAME_CANDIDATES_STUDENT)
        try:
            phone_key = pick_header(fieldnames, PHONE_CANDIDATES_STUDENT)
        except KeyError:
            phone_key = None  # tolerate missing, all phones will be virtual

        name_to_phone: Dict[str, str] = {}
        for row in reader:
            name = (row.get(name_key, "") or "").strip()
            phone = (row.get(phone_key, "") or "").strip() if phone_key else ""
            if not name:
                continue
            if name not in name_to_phone and phone:
                name_to_phone[name] = phone
            elif phone and not name_to_phone.get(name):
                name_to_phone[name] = phone
        return name_to_phone


def read_fee_csv_with_header_detection(path: str) -> Tuple[List[str], List[Dict[str, str]]]:
    rows_raw: List[List[str]] = []
    with open(path, "r", encoding="utf-8-sig", newline="") as f:
        reader = csv.reader(f)
        for r in reader:
            rows_raw.append([c.strip() for c in r])

    # find header row: prefer the first row that contains any of the name candidates
    header_idx = None
    for idx, r in enumerate(rows_raw[:5]):
        cells = [c for c in r if c]
        if not cells:
            continue
        if any(any(cand in cell for cand in NAME_CANDIDATES_FEE) for cell in cells):
            header_idx = idx
            break
    if header_idx is None:
        # fallback: first non-empty row
        for idx, r in enumerate(rows_raw):
            if any(r):
                header_idx = idx
                break
    if header_idx is None:
        raise ValueError("Failed to detect header in fee CSV")

    header = rows_raw[header_idx]
    data_rows = rows_raw[header_idx + 1 :]

    # build list of dicts aligning to header length
    result_rows: List[Dict[str, str]] = []
    for r in data_rows:
        # pad or trim
        row = list(r) + [""] * (len(header) - len(r))
        row = row[: len(header)]
        result_rows.append({header[i]: row[i] for i in range(len(header))})

    # ensure header has at least one non-empty col; if leading empty first column, keep it to preserve structure
    return header, result_rows


def merge_and_write(name_to_phone: Dict[str, str]) -> None:
    fee_fields, fee_rows = read_fee_csv_with_header_detection(FEE_CSV_PATH)
    fee_name_key = pick_header(fee_fields, NAME_CANDIDATES_FEE)
    try:
        fee_phone_key = pick_header(fee_fields, PHONE_CANDIDATES_FEE)
    except KeyError:
        fee_phone_key = "家长电话"
        fee_fields = fee_fields + [fee_phone_key]

    merged_rows: List[Dict[str, str]] = []
    for row in fee_rows:
        name = (row.get(fee_name_key, "") or "").strip()
        src_phone = (name_to_phone.get(name, "") or "").strip()
        phone = src_phone if src_phone else generate_virtual_phone(name)
        row[fee_phone_key] = phone
        merged_rows.append(row)

    os.makedirs(os.path.dirname(OUT_CSV_PATH), exist_ok=True)
    with open(OUT_CSV_PATH, "w", encoding="utf-8-sig", newline="") as f_out:
        writer = csv.DictWriter(f_out, fieldnames=fee_fields)
        writer.writeheader()
        writer.writerows(merged_rows)


if __name__ == "__main__":
    mapping = build_student_phone_map()
    merge_and_write(mapping)
    print(f"Merged to {OUT_CSV_PATH}") 