/* 缴费记录页面的Select组件样式 */

/* 修复Select组件和其他表单元素的样式 */
.ant-select {
  width: 100%;
}

.ant-select-selector {
  display: flex !important;
  align-items: center !important;
  height: 32px !important;
  padding: 0 11px !important;
}

.payment-records-container {
  padding: 24px;
}

.payment-statistics {
  margin-bottom: 24px;
}

.payment-filter-bar {
  margin-bottom: 16px;
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
}

.payment-filter-buttons {
  margin-top: 24px;
  text-align: right;
}

.status-badge {
  margin-right: 0;
}

.payment-modal-divider {
  margin: 0 0 24px 0;
}

/* 解决模态框中分隔线重复的问题 */
.payment-receipt-modal .ant-modal-body {
  padding-top: 0;
}

.payment-receipt-modal .ant-divider {
  margin-top: 0;
}

.payment-receipt-descriptions {
  margin-top: 0;
}

/* 给删除确认模态框和编辑模态框添加相同的样式，解决分隔线重复问题 */
.ant-modal .ant-modal-body {
  padding-top: 0;
}

.ant-modal .ant-divider {
  margin-top: 0;
}

.payment-dropdown {
  min-width: 200px !important;
}

/* 统一的间距类 - 与打卡消课页面保持一致 */
.mb-0 {
  margin-bottom: 0 !important;
}

.mb-6 {
  margin-bottom: 24px !important;
}