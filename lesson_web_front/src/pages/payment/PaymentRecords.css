/* 缴费记录页面样式 */
.payment-management {
  padding: 0 4px;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.payment-management-card {
  margin-top: -12px;
  border-radius: 8px;
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.08);
}

.payment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 0;
}

/* 复制出勤记录页面的多选框样式 - 增加优先级 */
.payment-management .payment-search-bar,
.payment-search-bar {
  background: #fafafa !important;
  padding: 16px !important;
  border-radius: 6px !important;
  border: 1px solid #f0f0f0 !important;
  margin-bottom: 16px !important;
  width: 100% !important;
}

/* 表单项样式统一 */
.payment-management .payment-search-bar .ant-form-item,
.payment-search-bar .ant-form-item {
  margin-bottom: 0 !important;
}

.payment-management .payment-search-bar .ant-form-item-control,
.payment-search-bar .ant-form-item-control {
  line-height: 32px !important;
}

/* 搜索栏响应式样式 */
.payment-management .payment-search-bar .ant-input-search,
.payment-search-bar .ant-input-search {
  border-radius: 4px !important;
}

.payment-management .payment-search-bar .ant-select,
.payment-search-bar .ant-select {
  border-radius: 4px !important;
}

.payment-management .payment-search-bar .ant-picker,
.payment-search-bar .ant-picker {
  border-radius: 4px !important;
}

.payment-management .payment-search-bar .ant-picker-input input,
.payment-search-bar .ant-picker-input input {
  text-align: center !important;
}

.payment-management .payment-search-bar .ant-picker-range .ant-picker-input input,
.payment-search-bar .ant-picker-range .ant-picker-input input {
  text-align: center !important;
}

/* 按钮样式优化 */
.payment-management .payment-search-bar .ant-btn,
.payment-search-bar .ant-btn {
  border-radius: 4px !important;
  font-weight: 500 !important;
}

.payment-management .payment-search-bar .ant-btn-primary,
.payment-search-bar .ant-btn-primary {
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2) !important;
}

.payment-management .payment-search-bar .ant-btn-primary:hover,
.payment-search-bar .ant-btn-primary:hover {
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3) !important;
  transform: translateY(-1px) !important;
}

/* 多选课程下拉框样式优化 */
.payment-search-bar .ant-select-multiple .ant-select-selection-item {
  background: #e6f7ff !important;
  border: 1px solid #91d5ff !important;
  border-radius: 4px !important;
  color: #1890ff !important;
  font-size: 12px !important;
  padding: 2px 6px !important;
  margin: 1px 2px !important;
  max-width: 150px !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
  display: inline-flex !important;
  align-items: center !important;
}

.payment-search-bar .ant-select-multiple .ant-select-selection-item-content {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
  max-width: calc(100% - 16px) !important;
}

.payment-search-bar .ant-select-multiple .ant-select-selection-item-remove {
  color: #1890ff !important;
  font-size: 10px !important;
  margin-left: 4px !important;
  opacity: 0.7 !important;
  cursor: pointer !important;
}

.payment-search-bar .ant-select-multiple .ant-select-selection-item-remove:hover {
  opacity: 1 !important;
  color: #ff4d4f !important;
}

/* 下拉选项中选中项的样式 */
.payment-select-dropdown .ant-select-item-option-selected {
  background-color: #e6f7ff !important;
  color: #1890ff !important;
  font-weight: 500 !important;
}

.payment-select-dropdown .ant-select-item-option-selected:hover {
  background-color: #bae7ff !important;
}

/* 隐藏下拉选项中的勾选图标 */
.payment-select-dropdown .ant-select-item-option-selected .ant-select-item-option-state {
  display: none !important;
}

/* 防止选择框换行 */
.payment-management .payment-search-bar .ant-select-multiple .ant-select-selector,
.payment-search-bar .ant-select-multiple .ant-select-selector {
  flex-wrap: nowrap !important;
  overflow: hidden !important;
}

.payment-management .payment-search-bar .ant-select-multiple .ant-select-selection-overflow,
.payment-search-bar .ant-select-multiple .ant-select-selection-overflow {
  flex-wrap: nowrap !important;
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .payment-search-bar .ant-row {
    flex-wrap: wrap !important;
  }
  
  .payment-search-bar .ant-col {
    min-width: auto !important;
    margin-bottom: 8px;
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .payment-management {
    padding: 0;
  }

  .payment-management-card {
    margin-top: -8px;
  }
} 