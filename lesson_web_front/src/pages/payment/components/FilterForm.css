.filter-toolbar {
  background: #fafafa;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  margin-bottom: 16px;
  width: 100%;
}

/* 表单项样式统一 */
.filter-toolbar .ant-form-item {
  margin-bottom: 0;
}

.filter-toolbar .ant-form-item-control {
  line-height: 32px;
}

/* 搜索栏响应式样式 */
.filter-toolbar .ant-input-search {
  border-radius: 4px;
}

.filter-toolbar .ant-select {
  border-radius: 4px;
}

.filter-toolbar .ant-picker {
  border-radius: 4px;
}

.filter-toolbar .ant-picker-input input {
  text-align: center;
}

.filter-toolbar .ant-picker-range .ant-picker-input input {
  text-align: center;
}

/* 按钮样式优化 */
.filter-toolbar .ant-btn {
  border-radius: 4px;
  font-weight: 500;
}

.filter-toolbar .ant-btn-primary {
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

.filter-toolbar .ant-btn-primary:hover {
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
  transform: translateY(-1px);
}

/* 多选课程下拉框样式优化 */
.filter-toolbar .ant-select-multiple .ant-select-selection-item {
  background: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 4px;
  color: #1890ff;
  font-size: 12px;
  padding: 0 6px;
  height: 24px;
  margin: 0 2px;
  display: inline-flex;
  align-items: center;
  white-space: nowrap;
}

/* 缴费类型选框样式 - 确保标签能占据整个选择框宽度 */
.filter-toolbar .ant-form-item:nth-child(3) .ant-select-multiple .ant-select-selector {
  width: 100% !important;
  display: flex !important;
  flex-wrap: nowrap !important;
}

.filter-toolbar .ant-form-item:nth-child(3) .ant-select-multiple .ant-select-selection-overflow {
  width: 100% !important;
  display: flex !important;
  flex-wrap: nowrap !important;
}

/* 缴费类型标签强制占据合适宽度 */
.payment-type-tag-large {
  min-width: 80px !important;
  width: 80px !important;
  max-width: 80px !important;
  display: inline-flex !important;
  box-sizing: border-box !important;
}

/* 缴费类型选择框内的标签容器样式 - 调整为自适应宽度 */
.filter-toolbar .ant-form-item:nth-child(3) .ant-select-multiple .ant-select-selection-item {
  display: inline-flex;
  align-items: center;
  box-sizing: border-box;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 下拉选项中选中项的样式 */
.ant-select-dropdown .ant-select-item-option-selected {
  background-color: #e6f7ff !important;
  color: #1890ff !important;
  font-weight: 500;
}

.ant-select-dropdown .ant-select-item-option-selected:hover {
  background-color: #bae7ff !important;
}

/* 隐藏下拉选项中的勾选图标 */
.ant-select-dropdown .ant-select-item-option-selected .ant-select-item-option-state {
  display: none !important;
}

/* 防止选择框换行 */
.filter-toolbar .ant-select-multiple .ant-select-selector {
  flex-wrap: nowrap !important;
  overflow: hidden;
  display: flex !important;
  align-items: center !important;
  height: 32px !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.filter-toolbar .ant-select-multiple .ant-select-selection-item {
  height: 24px;
  line-height: 24px;
  display: inline-flex;
  align-items: center;
}

.filter-toolbar .ant-select-multiple .ant-select-selection-overflow {
  flex-wrap: nowrap;
  display: flex;
  align-items: center;
}

/* 缩短缴费类型选择框的+1标签 - 多重选择器确保生效 */
.filter-toolbar .ant-form-item:nth-child(3) .ant-select-multiple .ant-select-selection-overflow-item-rest .ant-select-selection-item,
.filter-toolbar .ant-form-item:nth-child(3) .ant-select-multiple .ant-select-selection-overflow-item-rest span,
.filter-toolbar .ant-form-item:nth-child(3) .ant-select-selection-overflow-item-rest .ant-select-selection-item,
.filter-toolbar .ant-form-item:nth-child(3) .ant-select-selection-overflow-item-rest span {
  width: 30px !important;
  min-width: 30px !important;
  max-width: 30px !important;
  background: #f0f0f0 !important;
  border: 1px solid #d9d9d9 !important;
  border-radius: 4px !important;
  color: #666 !important;
  font-size: 12px !important;
  padding: 2px 6px !important;
  margin: 1px 2px !important;
  text-align: center !important;
  display: inline-block !important;
  box-sizing: border-box !important;
}

.filter-toolbar .ant-select-multiple .ant-select-selection-overflow .ant-select-selection-overflow-item:first-child {
  margin-left: 0;
}

.filter-toolbar .ant-select-multiple .ant-select-selection-overflow-item {
  display: flex;
  align-items: center;
}

 