/* 支出记录页面的Select组件样式 */

/* 强制显示所有选择器的placeholder */
.ant-select .ant-select-selection-placeholder,
.ant-select-selector .ant-select-selection-placeholder,
.ant-select-selection-placeholder,
.custom-select .ant-select-selection-placeholder,
.expense-management-container .ant-select-selection-placeholder,
#expenseCategorySelect .ant-select-selection-placeholder {
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
  color: rgba(0, 0, 0, 0.4) !important;
  position: absolute !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  left: 11px !important;
  right: auto !important;
  transition: none !important;
  pointer-events: none !important;
}

/* 确保下拉菜单有足够的宽度 */
.custom-select-dropdown,
.ant-select-dropdown {
  min-width: 120px !important;
}

/* 修复Select组件和其他表单元素的样式 */
.ant-select {
  width: 100%;
}

.ant-select-selector {
  display: flex !important;
  align-items: center !important;
  height: 32px !important;
  padding: 0 11px !important;
}

/* 特别强调支出类别的选择器 */
#expenseCategorySelect .ant-select-selection-placeholder {
  z-index: 100 !important;
  font-size: 14px !important;
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
  color: rgba(0, 0, 0, 0.4) !important;
}

/* 收支管理页面样式 */
.expense-management {
  padding: 0 4px;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.expense-management-card {
  margin-top: -12px;
  border-radius: 8px;
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.08);
}

/* 收支管理界面容器 */
.expense-management-container {
  padding: 24px;
}

/* 日期选择器文字居中 */
.table-toolbar .ant-picker-input input {
  text-align: center;
}

.table-toolbar .ant-picker-range .ant-picker-input input {
  text-align: center;
}

/* 解决收支管理页面模态框中分隔线重复的问题 */
.ant-modal .ant-modal-body {
  padding-top: 0;
}

.ant-modal .ant-divider {
  margin-top: 0;
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .table-toolbar .ant-row {
    flex-wrap: wrap !important;
  }
  
  .table-toolbar .ant-col {
    min-width: auto !important;
    margin-bottom: 8px;
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .expense-management {
    padding: 0;
  }

  .expense-management-card {
    margin-top: -8px;
  }
} 