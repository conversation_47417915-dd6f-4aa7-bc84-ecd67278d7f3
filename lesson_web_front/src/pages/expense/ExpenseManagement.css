/* 收支管理页面样式 */
.expense-management {
  padding: 0 4px;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.expense-management-card {
  margin-top: -12px;
  border-radius: 8px;
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.08);
}

.expense-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 0 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.expense-actions {
  display: flex;
  align-items: center;
}

.add-expense-button {
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.08);
  padding: 0 16px;
  height: 32px;
}

.add-expense-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 搜索栏响应式样式 */
.search-actions {
  width: 100%;
  display: flex;
  justify-content: flex-end;
}

.search-actions .ant-space {
  white-space: nowrap;
  overflow: hidden;
}

.search-actions .ant-btn {
  min-width: auto;
  padding: 4px 8px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .expense-management {
    padding: 0;
  }

  .expense-management-card {
    margin-top: -8px;
  }

  /* 在小屏幕上让按钮居中 */
  .search-actions {
    justify-content: center;
  }

  /* 在小屏幕上调整头部布局 */
  .expense-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .expense-header .page-title {
    text-align: center;
    margin-bottom: 0;
  }

  .expense-actions {
    justify-content: center;
  }
}

/* 中等屏幕调整 */
@media (min-width: 769px) and (max-width: 1024px) {
  .search-actions {
    justify-content: flex-end;
  }
}

/* 大屏幕保持原样 */
@media (min-width: 1025px) {
  .search-actions {
    justify-content: flex-end;
  }
} 