.attendance-table-toolbar {
  background: #fafafa;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  margin-bottom: 16px;
  width: 100%;
}

/* 表单项样式统一 */
.attendance-table-toolbar .ant-form-item {
  margin-bottom: 0;
}

.attendance-table-toolbar .ant-form-item-control {
  line-height: 32px;
}

/* 搜索栏响应式样式 */
.attendance-table-toolbar .ant-input-search {
  border-radius: 4px;
}

.attendance-table-toolbar .ant-select {
  border-radius: 4px;
}

.attendance-table-toolbar .ant-picker {
  border-radius: 4px;
}

.attendance-table-toolbar .ant-picker-input input {
  text-align: center;
}

.attendance-table-toolbar .ant-picker-range .ant-picker-input input {
  text-align: center;
}

/* 按钮样式优化 */
.attendance-table-toolbar .ant-btn {
  border-radius: 4px;
  font-weight: 500;
}

.attendance-table-toolbar .ant-btn-primary {
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

.attendance-table-toolbar .ant-btn-primary:hover {
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
  transform: translateY(-1px);
}

/* 多选课程下拉框样式优化 */
.attendance-table-toolbar .ant-select-multiple .ant-select-selection-item {
  background: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 4px;
  color: #1890ff;
  font-size: 12px;
  padding: 0 6px;
  margin: 0 2px;
  height: 24px;
  line-height: 24px;
  display: inline-flex;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.attendance-table-toolbar .ant-select-multiple .ant-select-selection-item-content {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: calc(100% - 16px);
}

.attendance-table-toolbar .ant-select-multiple .ant-select-selection-item-remove {
  color: #1890ff;
  font-size: 10px;
  margin-left: 4px;
  opacity: 0.7;
}

.attendance-table-toolbar .ant-select-multiple .ant-select-selection-item-remove:hover {
  opacity: 1;
  color: #ff4d4f;
}

/* 下拉选项中选中项的样式 */
.ant-select-dropdown .ant-select-item-option-selected {
  background-color: #e6f7ff !important;
  color: #1890ff !important;
  font-weight: 500;
}

.ant-select-dropdown .ant-select-item-option-selected:hover {
  background-color: #bae7ff !important;
}

/* 隐藏下拉选项中的勾选图标 */
.ant-select-dropdown .ant-select-item-option-selected .ant-select-item-option-state {
  display: none !important;
}

/* 防止选择框换行 */
.attendance-table-toolbar .ant-select-multiple .ant-select-selector {
  flex-wrap: nowrap !important;
  overflow: hidden;
  display: flex !important;
  align-items: center !important;
  height: 32px !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.attendance-table-toolbar .ant-select-multiple .ant-select-selection-overflow {
  flex-wrap: nowrap;
  display: flex;
  align-items: center;
}

.attendance-table-toolbar .ant-select-multiple .ant-select-selection-overflow .ant-select-selection-overflow-item:first-child {
  margin-left: 0;
}

.attendance-table-toolbar .ant-select-multiple .ant-select-selection-overflow-item {
  display: flex;
  align-items: center;
}

.attendance-table-toolbar .ant-select-multiple .ant-select-selection-item {
  height: 24px;
  line-height: 24px;
  display: inline-flex;
  align-items: center;
} 