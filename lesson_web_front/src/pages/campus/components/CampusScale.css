:root {
  --label-width: 80px;
  --icon-size: 24px;
  --value-width: 30px;
}

/* 合并容器样式 */
.campus-scale-combined-container {
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  overflow: hidden;
  background-color: white;
  border: 1px solid #e8e8e8;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);
  margin: 0 auto;
  width: 100%;
  max-width: 220px;
  position: relative;
}

/* 内容区域样式 */
.campus-scale-combined-content {
  display: flex;
  flex-direction: column;
}

/* 合并项目样式 */
.scale-combined-item {
  display: flex;
  align-items: center;
  padding: 5px 15px;
  transition: all 0.2s ease;
  border-bottom: 1px solid #f0f0f0;
}

/* 最后一个项目没有底部边框 */
.scale-combined-item:last-child {
  border-bottom: none;
}

/* 鼠标悬停效果 */
.scale-combined-item:hover {
  background-color: #f9f9f9;
}

/* 学员项目样式 */
.scale-combined-item:nth-child(1) {
  border-left: 3px solid #3498db;
}

/* 教练项目样式 */
.scale-combined-item:nth-child(2) {
  border-left: 3px solid #2ecc71;
}

/* 课时项目样式 */
.scale-combined-item:nth-child(3) {
  border-left: 3px solid #f39c12;
}

.scale-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: var(--icon-size);
  height: var(--icon-size);
  border-radius: 50%;
  margin-right: 8px;
  flex-shrink: 0;
}

.scale-icon-student {
  background-color: rgba(52, 152, 219, 0.15);
}

.scale-icon-coach {
  background-color: rgba(46, 204, 113, 0.15);
}

.scale-icon-lesson {
  background-color: rgba(243, 156, 18, 0.15);
}

.scale-label {
  font-size: 14px;
  color: #333;
  letter-spacing: 0.5px;
  white-space: nowrap;
  padding-left: 5px;
  width: var(--label-width);
  display: inline-block;
  text-align: left;
}

.scale-value {
  font-weight: 600;
  margin-left: auto;
  min-width: var(--value-width);
  text-align: right;
  flex-shrink: 0;
}

.scale-value-student {
  color: #3498db;
}

.scale-value-coach {
  color: #2ecc71;
}

.scale-value-lesson {
  color: #f39c12;
}

/* 保留旧的容器样式以兼容其他地方的使用 */
.campus-scale-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.scale-item {
  display: flex;
  align-items: center;
  padding: 6px 10px;
  border-radius: 6px;
  transition: all 0.2s ease;
  justify-content: space-between;
}

.scale-item:hover {
  transform: translateX(2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.scale-item-student {
  background-color: rgba(52, 152, 219, 0.08);
  border: 1px solid rgba(52, 152, 219, 0.15);
}

.scale-item-coach {
  background-color: rgba(46, 204, 113, 0.08);
  border: 1px solid rgba(46, 204, 113, 0.15);
}

.scale-item-lesson {
  background-color: rgba(243, 156, 18, 0.08);
  border: 1px solid rgba(243, 156, 18, 0.15);
}
