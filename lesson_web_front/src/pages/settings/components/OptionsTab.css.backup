/* 自定义左侧选项卡样式 */
.settings-vertical-tabs {
  height: 100%;
  width: 100% !important;
  display: flex;
}

.settings-vertical-tabs .ant-tabs-nav {
  width: 200px !important;
  background-color: #3a56d4;
  background-image: linear-gradient(to bottom, #4361ee, #3a56d4);
  border-right: none;
  min-width: 200px !important;
  flex-shrink: 0;
  padding: 24px 0;
  box-shadow: 4px 0 10px rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 2;
}

.settings-vertical-tabs .ant-tabs-tab {
  margin: 0 !important;
  padding: 0 !important;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  transition: all 0.3s;
  position: relative;
  border: none !important;
  background-color: transparent !important;
  margin-bottom: 8px !important;
}

.settings-vertical-tabs .ant-tabs-tab-btn {
  padding: 0 24px !important;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  text-align: left;
}

.settings-vertical-tabs .ant-tabs-tab-active {
  background-color: rgba(255, 255, 255, 0.15) !important;
}

.settings-vertical-tabs .ant-tabs-tab:hover {
  color: #ffffff;
}

.settings-vertical-tabs .ant-tabs-ink-bar {
  display: none;
}

.settings-vertical-tabs .ant-tabs-tab .tab-item {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 12px;
  width: 100%;
}

.settings-vertical-tabs .ant-tabs-tab-active .tab-item {
  color: #ffffff;
  font-weight: 600;
}

.settings-vertical-tabs .ant-tabs-tab .tab-item .anticon {
  font-size: 18px;
}

/* 激活指示器 */
.settings-vertical-tabs .ant-tabs-tab-active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  height: 70%;
  width: 4px;
  background-color: #ffffff;
  display: block;
  border-radius: 0 4px 4px 0;
}

/* 内容区域样式 */
.settings-vertical-tabs .ant-tabs-content-holder {
  flex: 1;
  background-color: #ffffff;
  padding: 0;
}

.options-tab {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  background-color: #f8f9fd;
  padding-top: 20px;
}

.options-tab > .ant-tabs {
  width: 100%;
  max-width: 1200px;
  display: flex;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.options-tab-content-wrapper {
  width: 100%;
  max-width: 1200px;
  padding: 0;
  box-sizing: border-box;
}

.option-list-container {
  margin-top: 0;
  padding: 24px;
}

/* 选项标题 */
.option-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  width: 100% !important;
  padding-bottom: 12px;
  border-bottom: 1px solid #edf2f7;
}

.option-header .ant-typography {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 卡片样式 */
.option-card {
  border-radius: 10px;
  transition: all 0.3s ease;
  overflow: hidden;
  border: 1px solid #edf2f7;
  background-color: #ffffff;
  height: 100%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.option-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(67, 97, 238, 0.12);
  border-color: rgba(67, 97, 238, 0.2);
}

/* 添加按钮样式 */
.option-list-container .ant-btn-primary {
  height: 36px;
  line-height: 36px;
  padding: 0 20px;
  background: linear-gradient(135deg, #4361ee, #3a56d4);
  border: none;
  box-shadow: 0 4px 12px rgba(67, 97, 238, 0.2);
  border-radius: 8px;
  font-weight: 500;
}

.option-list-container .ant-btn-primary:hover {
  background: linear-gradient(135deg, #3a56d4, #2d47c5);
  box-shadow: 0 6px 16px rgba(67, 97, 238, 0.3);
  transform: translateY(-2px);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .settings-vertical-tabs .ant-tabs-nav {
    width: 160px !important;
    min-width: 160px !important;
  }
  
  .options-tab > .ant-tabs {
    border-radius: 8px;
  }
}

/* 表单元素样式 */
.option-list-container .ant-form-item-label > label {
  font-weight: 500;
  color: #2d3748;
}

.option-list-container .ant-form-item {
  margin-bottom: 16px;
}

/* 空状态提示 */
.ant-empty {
  margin: 20px 0;
}

.ant-empty-image {
  opacity: 0.7;
}

.ant-empty-description {
  color: #909090;
  font-size: 14px;
}

/* 左侧边栏整体样式 */
.settings-vertical-tabs .ant-tabs-nav {
  background-color: #fcfdff;
  border-right: 1px solid #f0f0f0;
}

.settings-vertical-tabs .ant-tabs-content {
  padding: 0;
  width: 100% !important;
  height: 100%;
}

/* 左侧边栏选中项样式 */
.settings-vertical-tabs .ant-tabs-tab-active {
  position: relative;
  z-index: 2;
}

.settings-vertical-tabs .ant-tabs-tab-active::after {
  /* 移除右侧白色覆盖线 */
  display: none;
}

/* 左侧导航样式修复 */
.settings-vertical-tabs .ant-tabs-content {
  padding: 0;
  width: 100% !important;
}

.settings-vertical-tabs .ant-tabs-tabpane {
  padding: 0 20px;
  width: 100% !important;
}

/* 优化卡片列表布局 */
.option-list-container .ant-row {
  width: 100% !important;
  margin: 0 -8px !important;
}

.option-list-container .ant-col {
  padding: 0 8px !important;
}

.option-card {
  height: 100%;
}

.option-card:hover {
  box-shadow: 0 6px 16px rgba(107, 154, 238, 0.08);
}

.ant-btn-primary {
  background: #4a6cf7;
  border: none;
}

.ant-divider {
  border-top-color: #f0f0f0;
} 