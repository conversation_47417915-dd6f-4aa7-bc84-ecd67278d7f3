/* 系统设置页面样式 */
.settings-management {
  padding: 0 4px;
}

.settings-management-card {
  margin-top: -12px;
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.08);
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0;
}

.settings-title {
  margin: 0;
  font-weight: 600;
}

/* 头部标签样式 */
.header-tabs {
  flex: 1;
  margin-left: 20px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.header-tabs .ant-tabs {
  width: auto;
}

.header-tabs .ant-tabs-nav {
  margin-bottom: 0;
  width: auto;
  display: flex;
  justify-content: flex-end;
}

.header-tabs .ant-tabs-nav-wrap {
  display: flex;
  justify-content: flex-end;
}

.header-tabs .ant-tabs-nav-list {
  display: flex;
  justify-content: flex-end;
}

.header-tabs .ant-tabs-tab {
  padding: 8px 16px;
  font-size: 15px;
}

.settings-tabs-content {
  margin-top: 20px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .settings-management {
    padding: 0;
  }

  .settings-management-card {
    margin-top: -8px;
  }

  .settings-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-tabs {
    margin-left: 0;
    width: 100%;
    justify-content: flex-start;
  }

  .header-tabs .ant-tabs-nav-wrap {
    justify-content: flex-start;
  }

  .header-tabs .ant-tabs-nav-list {
    justify-content: flex-start;
  }

  .header-tabs .ant-tabs-tab {
    padding: 6px 12px;
    font-size: 14px;
  }
}

/* 原有样式保留用于内容区域 */
.settings-management .ant-form-item-label > label {
  font-weight: 500;
  color: #333;
}

.settings-management .ant-divider {
  margin: 16px 0 24px;
}

.settings-management .option-list {
  max-height: 300px;
  overflow-y: auto;
  border-radius: 8px;
  margin-bottom: 16px;
  border: 1px solid #f0f0f0;
}

.settings-management .option-item {
  transition: all 0.3s ease;
  padding: 12px 16px;
}

.settings-management .option-item:hover {
  background-color: #f5f7fa;
}

.settings-management .option-input {
  flex: 1;
  transition: all 0.3s ease;
}

.settings-management .option-input:focus {
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
  outline: none;
}

.settings-management .backup-table th,
.settings-management .backup-table td {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.settings-management .backup-table tr:hover {
  background-color: #f5f7fa;
}

.settings-management .ant-btn {
  border-radius: 6px;
  font-weight: 500;
}

.settings-management .ant-btn-primary {
  background: #1677ff;
  border: none;
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.2);
}

.settings-management .ant-btn-primary:hover {
  background: #4096ff;
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
}

.settings-management .ant-form {
  animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Toast样式 */
.toast {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: #28a745;
  color: white;
  padding: 10px 20px;
  border-radius: 4px;
  animation: fadeIn 0.3s ease forwards;
  z-index: 1000;
}