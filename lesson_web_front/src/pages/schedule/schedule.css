/* 课表视图整体容器 - 专业高档风格 */
.schedule-view {
  padding: 0 4px;
  background: #fafbfc;
}

.schedule-management-card {
  margin-top: -12px;
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.06);
  border: 1px solid #e8eaed;
  border-radius: 8px;
  background: #ffffff;
}

/* 课表内容区域 - 统一整体设计 */
.schedule-content {
  display: flex;
  flex-direction: column;
  gap: 0;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  padding: 0;
  border-radius: 8px;
  margin: 0;
  border: none;
  overflow: visible;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
}

/* 图例样式 - 与表格一体化设计 */
.legend {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  margin-bottom: 0;
  padding: 20px 24px;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border-radius: 8px 8px 0 0;
  border: none;
  box-shadow: none;
  position: relative;
}

.legend::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 24px;
  right: 24px;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(218, 220, 224, 0.8) 10%, rgba(218, 220, 224, 0.8) 90%, transparent 100%);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 1px solid #dadce0;
  border-radius: 4px;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  user-select: none;
  position: relative;
  font-weight: 500;
  min-height: 40px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.legend-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: transparent;
  border-radius: 4px 4px 0 0;
  transition: all 0.25s ease;
}

.legend-item:hover {
  background: linear-gradient(135deg, #f1f3f4 0%, #e8eaed 100%);
  border-color: #bdc1c6;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.legend-item:hover::before {
  background: linear-gradient(90deg, #1a73e8 0%, #4285f4 100%);
}

.legend-item.active {
  background: linear-gradient(135deg, #1a73e8 0%, #4285f4 100%);
  color: #ffffff;
  border-color: #1557b0;
  box-shadow: 0 4px 16px rgba(26, 115, 232, 0.25);
  transform: translateY(-1px);
}

.legend-item.active::before {
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.5) 100%);
}

.legend-item.active:hover {
  background: linear-gradient(135deg, #1557b0 0%, #1a73e8 100%);
  border-color: #1557b0;
  box-shadow: 0 6px 20px rgba(26, 115, 232, 0.3);
}

.legend-color {
  width: 14px;
  height: 14px;
  border-radius: 2px;
  border: 2px solid rgba(255, 255, 255, 0.9);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
  flex-shrink: 0;
}

.legend-item span {
  font-size: 14px;
  font-weight: 500;
  color: #3c4043;
  letter-spacing: 0.1px;
  line-height: 1.2;
}

.legend-item.active span {
  color: #ffffff;
  font-weight: 600;
}

/* 课表容器 - 与图例一体化 */
.schedule-container {
  overflow-x: auto;
  overflow-y: visible;
  border: none;
  border-radius: 0;
  background: transparent;
  box-shadow: none;
  margin: 0;
  padding: 0;
}

/* 课表网格 - 精致表格设计 */
.schedule {
  display: grid;
  grid-template-columns: 140px repeat(7, 1fr);
  min-width: 1200px;
  background: #ffffff;
  border-radius: 0;
  overflow: visible;
  border: 1px solid #e8eaed;
  border-top: none;
}

.schedule-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%);
  border-right: 1px solid #e8eaed;
  border-bottom: 1px solid #e8eaed;
  padding: 16px 12px;
  text-align: center;
  font-weight: 600;
  font-size: 15px;
  color: #202124;
  letter-spacing: 0.25px;
  position: relative;
}

.schedule-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(232, 234, 237, 0.6) 20%, rgba(232, 234, 237, 0.6) 80%, transparent 100%);
}

.schedule-time {
  background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%);
  border-right: 1px solid #e8eaed;
  border-bottom: 1px solid #e8eaed;
  padding: 16px 12px;
  text-align: center;
  font-weight: 600;
  font-size: 14px;
  color: #5f6368;
  letter-spacing: 0.25px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.schedule-cell {
  border-right: 1px solid #e8eaed;
  border-bottom: 1px solid #e8eaed;
  padding: 12px;
  min-height: 100px;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  gap: 4px;
  transition: all 0.3s ease;
  position: relative;
}

/* 当单元格只有一个课程块时，调整为居中显示 */
.schedule-cell:has(.student-info:only-child) {
  justify-content: center;
  gap: 0;
  padding: 6px 12px;
}

/* 如果浏览器不支持:has()选择器，使用这个备用方案 */
.schedule-cell.single-item {
  justify-content: center;
  gap: 0;
  padding: 6px 12px;
}

/* 最后一列去掉右边框，避免与表格外边框重叠 */
.schedule-cell:last-child {
  border-right: none;
}

/* 表格头部最后一列去掉右边框，避免与表格外边框重叠 */
.schedule-header:last-child {
  border-right: none;
}

/* 第一行去掉上边框，避免与表格外边框重叠 */
/* 第一行的所有元素去掉上边框 - 包括空白格子和星期标题 */
.schedule > div:nth-child(-n+8) {
  border-top: none !important;
}

.schedule-cell::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(26, 115, 232, 0.02) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.schedule-cell:hover::before {
  opacity: 1;
}

.schedule-cell:hover {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  box-shadow: inset 0 0 0 1px rgba(26, 115, 232, 0.1);
}

/* 学生/课程信息样式 - 现代卡片设计 */
.student-info {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
  border: 1px solid #dadce0 !important;
  border-left: 4px solid #34a853 !important;
  border-radius: 4px !important;
  padding: 8px 12px !important;
  margin: 0 !important;
  font-size: 13px !important;
  line-height: 1.6 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
  position: relative !important;
  cursor: pointer !important;
}

/* 课程信息卡片hover效果 */
.student-info:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12) !important;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%) !important;
}

/* 自定义tooltip样式 */
.custom-tooltip {
  position: fixed;
  background: #1890ff;
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 99999;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.custom-tooltip.show {
  opacity: 1;
}

.custom-tooltip::before {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: #1890ff;
}

.student-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(52, 168, 83, 0.05) 100%);
  border-radius: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.student-info:hover::before {
  opacity: 1;
}

.student-info:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12) !important;
  transform: translateY(-2px) !important;
  background: linear-gradient(135deg, #ffffff 0%, #f1f3f4 100%) !important;
  border-color: #bdc1c6 !important;
}

.student-info .name {
  font-weight: 600;
  color: #202124;
  margin-bottom: 6px;
  font-size: 14px;
  letter-spacing: 0.25px;
  line-height: 1.3;
}

.student-info .details {
  color: #5f6368;
  font-size: 12px;
  line-height: 1.5;
  margin-bottom: 4px;
  font-weight: 400;
}

.student-info .course-info {
  color: #1a73e8;
  font-weight: 500;
  margin-top: 4px;
  font-size: 12px;
  letter-spacing: 0.25px;
  line-height: 1.4;
}

.student-info .description {
  color: #80868b;
  font-style: normal;
  margin-top: 4px;
  font-size: 11px;
  font-weight: 400;
  line-height: 1.5;
}

/* 空状态样式 - 优雅的空状态 */
.schedule-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 240px;
  color: #80868b;
  font-size: 16px;
  font-weight: 400;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 12px;
  border: 2px dashed #dadce0;
  letter-spacing: 0.25px;
}

/* 教练颜色主题 - 现代调色板 */
.student-info.coach-1 { 
  border-left-color: #ea4335 !important;
}
.student-info.coach-1::before {
  background: linear-gradient(135deg, transparent 0%, rgba(234, 67, 53, 0.05) 100%);
}

.student-info.coach-2 { 
  border-left-color: #4285f4 !important;
}
.student-info.coach-2::before {
  background: linear-gradient(135deg, transparent 0%, rgba(66, 133, 244, 0.05) 100%);
}

.student-info.coach-3 { 
  border-left-color: #34a853 !important;
}
.student-info.coach-3::before {
  background: linear-gradient(135deg, transparent 0%, rgba(52, 168, 83, 0.05) 100%);
}

.student-info.coach-4 { 
  border-left-color: #fbbc04 !important;
}
.student-info.coach-4::before {
  background: linear-gradient(135deg, transparent 0%, rgba(251, 188, 4, 0.05) 100%);
}

.student-info.coach-5 { 
  border-left-color: #9334e6 !important;
}
.student-info.coach-5::before {
  background: linear-gradient(135deg, transparent 0%, rgba(147, 52, 230, 0.05) 100%);
}

.student-info.coach-6 { 
  border-left-color: #06b6d4 !important;
}
.student-info.coach-6::before {
  background: linear-gradient(135deg, transparent 0%, rgba(6, 182, 212, 0.05) 100%);
}

.student-info.coach-7 { 
  border-left-color: #ef4444 !important;
}
.student-info.coach-7::before {
  background: linear-gradient(135deg, transparent 0%, rgba(239, 68, 68, 0.05) 100%);
}

.student-info.coach-8 { 
  border-left-color: #8b5cf6 !important;
}
.student-info.coach-8::before {
  background: linear-gradient(135deg, transparent 0%, rgba(139, 92, 246, 0.05) 100%);
}

/* 响应式设计 - 优化移动端体验 */
@media (max-width: 768px) {
  .schedule-view {
    padding: 0;
    background: #fafbfc;
  }

  .schedule-management-card {
    margin-top: -8px;
    border-radius: 6px;
    box-shadow: 0 1px 8px rgba(0, 0, 0, 0.04);
  }
  
  .schedule-content {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    padding: 0;
    border-radius: 8px;
    margin: 16px 0;
  }
  
  .legend {
    padding: 16px 20px;
    gap: 6px;
    margin-bottom: 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%);
    border-bottom: 1px solid #e8eaed;
  }
  
  .legend-item {
    padding: 8px 12px;
    font-size: 13px;
    min-height: 36px;
  }
  
  .schedule {
    grid-template-columns: 100px repeat(7, 120px);
  }
  
  .schedule-header,
  .schedule-time {
    padding: 10px 6px;
    font-size: 12px;
  }
  
  .schedule-cell {
    padding: 8px;
    min-height: 80px;
  }
  
  .student-info {
    padding: 8px !important;
    font-size: 11px !important;
    margin-bottom: 4px !important;
  }
  
  .student-info .name {
    font-size: 12px;
  }
  
  .student-info .details {
    font-size: 10px;
  }
  
  .student-info .course-info {
    font-size: 11px;
  }
}

/* 这些样式已集成到主要的专业风格设计中 */

.page-title {
  font-size: 18px;
  font-weight: 600;
}

.filter-and-actions {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  width: 100%;
}

.schedule-controls {
  width: 100%;
  margin-right: 16px;
  flex: 1;
}

.action-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-left: 16px;
}

.schedule-cell.draggable .student-info {
  cursor: grab;
}

.schedule-cell.draggable .student-info:active {
  cursor: grabbing;
}

.schedule-cell.draggable .student-group {
  cursor: grab;
}

.schedule-cell.draggable .student-group:active {
  cursor: grabbing;
}

.schedule-cell.drop-target {
  background-color: rgba(52, 152, 219, 0.1);
  border: 2px dashed #3498db;
}

.student-group {
  display: block;
  width: 100%;
  margin: 4px 0;
  border: 2px solid;
  border-radius: 5px;
  overflow: hidden;
}

.student-group.coach-li { border-color: rgba(231, 76, 60, 0.4); }
.student-group.coach-wang { border-color: rgba(52, 152, 219, 0.4); }
.student-group.coach-zhang { border-color: rgba(46, 204, 113, 0.4); }
.student-group.coach-liu { border-color: rgba(243, 156, 18, 0.4); }

.student-group .student-info {
  margin: 0;
  border-radius: 0;
}

.student-group .student-info.group-first {
  border-bottom: 1px dashed rgba(0, 0, 0, 0.1);
}

.student-group .student-info.group-middle {
  border-bottom: 1px dashed rgba(0, 0, 0, 0.1);
}

.student-info .name {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 4px;
}

.student-info .course-info {
  margin-top: 2px;
  font-style: italic;
}

.change-item {
  border: 1px solid #eee;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 15px;
}

.change-header {
  font-weight: 600;
  margin-bottom: 10px;
  color: #3498db;
}

.change-detail {
  display: flex;
  gap: 20px;
  margin-bottom: 10px;
}

.change-before, .change-after {
  flex: 1;
}

.change-label {
  font-weight: 600;
  margin-bottom: 5px;
  color: #34495e;
}

.student-change {
  background-color: #f8f9fa;
  padding: 8px;
  border-radius: 4px;
  margin-bottom: 5px;
}

/* 样式优化已完成 */ 