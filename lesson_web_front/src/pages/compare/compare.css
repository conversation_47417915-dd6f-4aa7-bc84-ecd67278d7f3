.campus-analysis-container {
  padding: 24px;
}

.chart-card {
  margin-bottom: 24px;
}

.chart-card .ant-card-head {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.chart-card .ant-card-head-title {
  flex: 1 1 auto;
}

.chart-actions {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-end;
}

.chart-container {
  height: 300px;
  position: relative;
  width: 100%;
}

.filter-bar {
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 24px;
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-item label {
  margin-bottom: 0;
  font-weight: 500;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  text-align: center;
  transition: all 0.3s;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
}

.stat-revenue::before {
  background-color: #3498db;
}

.stat-profit::before {
  background-color: #2ecc71;
}

.stat-students::before {
  background-color: #f39c12;
}

.stat-coaches::before {
  background-color: #9b59b6;
}

.stat-title {
  font-size: 14px;
  color: #777;
  margin-bottom: 10px;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 10px;
}

.stat-trend {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  font-size: 12px;
}

.trend-up {
  color: #2ecc71;
}

.trend-down {
  color: #e74c3c;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .stats-cards {
    grid-template-columns: 1fr;
  }
  
  .filter-bar {
    flex-direction: column;
    align-items: flex-start;
  }
} 