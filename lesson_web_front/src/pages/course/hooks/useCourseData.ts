import { useState } from 'react';
import { message } from 'antd';
import { Course, CourseSearchParams, CourseType, CourseStatus } from '../types/course';
import { course as courseAPI } from '@/api/course';
import { CourseCreateRequest, CourseUpdateRequest } from '@/api/course/types';
import { CoachSimple } from '@/api/coach/types';
import { constants } from '@/api/constants';
import { getTypeNameById } from '../constants/courseOptions';

export const useCourseData = () => {
  // 存储所有课程数据，用于重置过滤器
  const [allCourses, setCourses] = useState<Course[]>([]);
  // 当前展示的过滤后的课程数据
  const [filteredCourses, setFilteredCourses] = useState<Course[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);

  // 添加课程
  const addCourse = async (values: CourseCreateRequest, coaches: CoachSimple[] = []) => {
    setLoading(true);
    try {
      // 确保有校区ID
      const campusId = values.campusId || Number(localStorage.getItem('currentCampusId') || '1');
      
      // 准备创建请求数据，确保包含所有必需字段
      const createData: CourseCreateRequest = {
        ...values,
        campusId: campusId // 确保campusId不为null
      };

      console.log('提交到API的创建数据:', createData);
      
      // 调用API添加课程
      const courseId = await courseAPI.add(createData);

      // 对于教练信息，优先从 coachFees 明细中推导；否则尝试 coachIds（兼容旧值）
      const courseCoaches = Array.isArray((values as any).coachFees) && (values as any).coachFees.length > 0
        ? (values as any).coachFees.map((x: any) => {
            const coach = coaches.find(c => c.id === Number(x.coachId));
            const id = Number(x.coachId);
            return { id, name: coach ? coach.name : `教练${id}` };
          })
        : (Array.isArray((values as any).coachIds) ? (values as any).coachIds.map((id: any) => {
            const coach = coaches.find(c => c.id === Number(id));
            return { id: Number(id), name: coach ? coach.name : `教练${id}` };
          }) : []);

      // 获取类型名称
      let typeName = getTypeNameById((values as any).typeId);
      console.log('添加课程使用的类型名称:', typeName);

      // 构造新课程对象，直接使用提交的表单数据
      const newCourse: Course = {
        id: courseId,
        name: (values as any).name,
        type: typeName, // 使用类型名称而不是ID
        status: (values as any).status,
        unitHours: (values as any).unitHours,
        totalHours: 0, // 新建课程总课时默认为0
        consumedHours: 0, // 新课程消耗课时为0
        price: (values as any).price,
        coachFee: Number((values as any).coachFee ?? 0),
        campusId: campusId, // 使用确保有值的campusId
        institutionId: 1, // 默认机构ID
        description: (values as any).description || '',
        createdTime: new Date().toISOString(),
        updateTime: new Date().toISOString(),
        coaches: courseCoaches
      };

      // 更新本地状态
      setCourses(prev => [newCourse, ...prev]);
      setFilteredCourses(prev => [newCourse, ...prev]);
      setTotal(prev => prev + 1);

      message.success('课程添加成功');
      return newCourse;
    } catch (error: any) {
      message.error(error.message || '添加课程失败');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // 更新课程
  const updateCourse = async (id: string, values: Omit<CourseUpdateRequest, 'id'>, coaches: CoachSimple[] = []) => {
    setLoading(true);
    try {
      // 确保有校区ID
      const campusId = values.campusId || Number(localStorage.getItem('currentCampusId') || '1');
      
      // 准备更新请求数据，确保包含所有必需字段
      const updateData: CourseUpdateRequest = {
        id,
        ...values,
        campusId: campusId // 确保campusId不为null
      };

      console.log('提交到API的更新数据:', updateData);
      
      // 调用API更新课程
      await courseAPI.update(updateData);

      // 根据更新的数据构造更新后的课程对象
      // 先找到原课程对象
      const originalCourse = filteredCourses.find(course => course.id === id);

      if (!originalCourse) {
        throw new Error('找不到要更新的课程');
      }

      // 处理课程类型显示
      let typeName = originalCourse.type; // 默认保持原来的类型名称
      
      // 如果类型发生变化，尝试查找匹配的名称
      if (values.typeId && Number(values.typeId) !== Number(originalCourse.type)) {
        console.log('类型ID已更改:', `${originalCourse.type} -> ${values.typeId}`);
        
        // 使用辅助函数获取类型名称
        typeName = getTypeNameById(values.typeId);
        console.log('获取到的类型名称:', typeName);
      } else {
        console.log('类型ID未变化，保持原类型名称:', typeName);
      }

      // 使用提交的表单值或保留原教练信息（编辑时不依赖 coachIds）
      const coachesWithNames = (() => {
        const cf: any = (values as any).coachFees;
        let ids: number[] = [];
        if (Array.isArray(cf)) {
          // 兼容数组形式 [{ coachId, coachFee }]
          ids = cf.map((x: any) => Number(x?.coachId)).filter((n: number) => !isNaN(n));
        } else if (cf && typeof cf === 'object') {
          // 兼容对象形式 { [coachId]: fee }
          ids = Object.keys(cf).map(id => Number(id)).filter((n: number) => !isNaN(n));
        }
        if (ids.length > 0) {
          return ids.map(id => {
            const originalCoach = originalCourse.coaches?.find(coach => coach.id === id);
            if (originalCoach?.name) return originalCoach;
            const coach = coaches.find(c => c.id === id);
            return { id, name: coach ? coach.name : `教练${id}` };
          });
        }
        return originalCourse.coaches || [];
      })();

      // 获取最新的状态值，确保它是正确的格式
      let statusValue = values.status;
      console.log('更新操作的状态值:', statusValue);

      // 构造更新后的课程对象
      const updatedCourse: Course = {
        ...originalCourse,
        name: values.name || originalCourse.name,
        type: typeName, // 使用课程类型名称而不是ID
        status: statusValue, // 使用新的状态值
        unitHours: values.unitHours || originalCourse.unitHours,
        totalHours: originalCourse.totalHours, // 更新不改总课时
        price: values.price || originalCourse.price,
        campusId: campusId, // 更新校区ID
        description: values.description !== undefined ? values.description : originalCourse.description,
        updateTime: new Date().toISOString(),
        coaches: coachesWithNames
      };

      console.log('更新后的课程对象:', updatedCourse);
      console.log('更新后的状态值:', updatedCourse.status);

      // 更新本地状态
      setCourses(prevCourses =>
        prevCourses.map(course => course.id === id ? updatedCourse : course)
      );

      setFilteredCourses(prevFiltered =>
        prevFiltered.map(course => course.id === id ? updatedCourse : course)
      );

      message.success('课程信息已更新');
      return updatedCourse;
    } catch (error: any) {
      message.error(error.message || '更新课程失败');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // 删除课程
  const deleteCourse = async (id: string) => {
    setLoading(true);
    try {
      // 调用API删除课程
      await courseAPI.delete(id);

      // 更新本地状态
      setCourses(prevCourses => prevCourses.filter(course => course.id !== id));
      setFilteredCourses(prevFiltered => prevFiltered.filter(course => course.id !== id));
      setTotal(prev => prev - 1);

      message.success('课程已删除');
    } catch (error: any) {
      message.error(error.message || '删除课程失败');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // 过滤课程数据
  const filterCourses = async (page: number, pageSize: number, params: CourseSearchParams) => {
    setLoading(true);

    try {
      // 构建 API 请求参数
      const apiParams: any = {
        pageNum: page,
        pageSize,
        keyword: params.searchText, // searchText -> keyword
        status: params.selectedStatus, // selectedStatus -> status
        campusId: params.campusId
      };

      // 课程类型
      if (params.selectedType && params.selectedType.length > 0) {
        if (params.selectedType.length === 1) {
          apiParams.typeId = params.selectedType[0];
        } else {
          apiParams.typeIds = params.selectedType;
        }
      }

      // 教练
      if (params.selectedCoach && params.selectedCoach.length > 0) {
        if (params.selectedCoach.length === 1) {
          apiParams.coachId = params.selectedCoach[0];
        } else {
          apiParams.coachIds = params.selectedCoach;
        }
      }

      // 排序参数，始终传递
      apiParams.sortField = params.sortField || '';
      apiParams.sortOrder = params.sortOrder || '';

      console.log('课程列表API参数:', apiParams);
      console.log('排序参数详情 - sortField:', params.sortField, 'sortOrder:', params.sortOrder);

      // 调用API获取课程列表
      const result = await courseAPI.getList(apiParams);

      // 更新本地状态
      setFilteredCourses(result.list as unknown as Course[]);
      setTotal(result.total);

      // 如果是第一次加载，也更新全局课程列表
      if (page === 1 && !params.searchText && 
          (!params.selectedType || params.selectedType.length === 0) && 
          !params.selectedStatus) {
        setCourses(result.list as unknown as Course[]);
      }

      return result.list;
    } catch (error: any) {
      message.error(error.message || '获取课程列表失败');
      console.error(error);
      return [];
    } finally {
      setLoading(false);
    }
  };

  // 重置过滤
  const resetFilters = async (page = 1, pageSize = 10) => {
    setLoading(true);
    try {
      // 获取当前校区ID
      const currentCampusId = localStorage.getItem('currentCampusId');
      const campusId = currentCampusId ? Number(currentCampusId) : undefined;

      // 调用API获取课程列表，只带校区ID筛选条件
      const result = await courseAPI.getList({
        pageNum: page,
        pageSize,
        campusId
      });

      // 更新本地状态
      setFilteredCourses(result.list as unknown as Course[]);
      setCourses(result.list as unknown as Course[]);
      setTotal(result.total);

      return result.list;
    } catch (error: any) {
      message.error(error.message || '重置过滤失败');
      console.error(error);
      return [];
    } finally {
      setLoading(false);
    }
  };

  return {
    courses: filteredCourses,
    allCourses,  // 返回所有课程数据，可用于高级筛选
    totalCount: total,
    loading,
    addCourse,
    updateCourse,
    deleteCourse,
    filterCourses,
    resetFilters
  };
};