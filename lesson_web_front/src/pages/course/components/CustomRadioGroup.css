/* 课程状态选择器样式 */
.course-status-segmented {
  width: 100% !important;
  height: 40px !important;
  background: transparent !important;
  border-radius: 8px !important;
  padding: 0 !important;
  box-shadow: none !important;
}

.course-status-segmented .ant-segmented-item {
  border-radius: 6px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  overflow: hidden !important;
  background: transparent !important;
  border: 1px solid #e8e8e8 !important;
  margin: 0 4px !important;
}

.course-status-segmented .ant-segmented-item:first-child {
  margin-left: 0 !important;
}

.course-status-segmented .ant-segmented-item:last-child {
  margin-right: 0 !important;
}

.course-status-segmented .ant-segmented-item:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  border-color: #1890ff !important;
}

.course-status-segmented .ant-segmented-item-selected {
  background: transparent !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  transform: translateY(-1px) !important;
  border-color: #1890ff !important;
  border-width: 2px !important;
}

/* 状态选项基础样式 */
.status-option {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 36px !important;
  padding: 0 12px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  position: relative !important;
}

.status-icon {
  margin-right: 6px !important;
  font-size: 16px !important;
  transition: all 0.3s ease !important;
}

.status-text {
  transition: all 0.3s ease !important;
}

/* 已发布状态样式 */
.status-published {
  color: #52c41a !important;
}

.course-status-segmented .ant-segmented-item:not(.ant-segmented-item-selected) .status-published {
  color: #52c41a !important;
}

.course-status-segmented .ant-segmented-item-selected .status-published {
  color: #52c41a !important;
  font-weight: 600 !important;
}

.course-status-segmented .ant-segmented-item:hover .status-published {
  color: #73d13d !important;
}

.course-status-segmented .ant-segmented-item-selected:hover .status-published {
  color: #52c41a !important;
}

/* 已暂停状态样式 */
.status-suspended {
  color: #fa8c16 !important;
}

.course-status-segmented .ant-segmented-item:not(.ant-segmented-item-selected) .status-suspended {
  color: #fa8c16 !important;
}

.course-status-segmented .ant-segmented-item-selected .status-suspended {
  color: #fa8c16 !important;
  font-weight: 600 !important;
}

.course-status-segmented .ant-segmented-item:hover .status-suspended {
  color: #ffa940 !important;
}

.course-status-segmented .ant-segmented-item-selected:hover .status-suspended {
  color: #fa8c16 !important;
}

/* 已终止状态样式 */
.status-terminated {
  color: #ff4d4f !important;
}

.course-status-segmented .ant-segmented-item:not(.ant-segmented-item-selected) .status-terminated {
  color: #ff4d4f !important;
}

.course-status-segmented .ant-segmented-item-selected .status-terminated {
  color: #ff4d4f !important;
  font-weight: 600 !important;
}

.course-status-segmented .ant-segmented-item:hover .status-terminated {
  color: #ff7875 !important;
}

.course-status-segmented .ant-segmented-item-selected:hover .status-terminated {
  color: #ff4d4f !important;
}

/* 悬停效果 */
.course-status-segmented .ant-segmented-item:hover .status-icon {
  transform: scale(1.1) !important;
}

/* 选中状态的图标动画 */
.course-status-segmented .ant-segmented-item-selected .status-icon {
  transform: scale(1.05) !important;
}

/* 禁用状态 */
.course-status-segmented.ant-segmented-disabled .status-option {
  color: #bfbfbf !important;
  cursor: not-allowed !important;
}

.course-status-segmented.ant-segmented-disabled .ant-segmented-item:hover {
  transform: none !important;
  box-shadow: none !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .course-status-segmented {
    height: 36px !important;
  }

  .status-option {
    height: 32px !important;
    padding: 0 8px !important;
    font-size: 13px !important;
  }

  .status-icon {
    font-size: 14px !important;
    margin-right: 4px !important;
  }
}
