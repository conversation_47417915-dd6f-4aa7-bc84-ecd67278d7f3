/* 学员打卡模态框样式 */

/* 确保所有表单项宽度一致 */
.attendance-form .ant-form-item {
  margin-bottom: 20px;
}

/* 确保所有输入框宽度一致 */
.attendance-form .ant-input,
.attendance-form .ant-picker,
.attendance-form .ant-select {
  width: 100% !important;
  border-radius: 6px !important;
}

/* 修复下拉选择框样式 */
.attendance-form .ant-select-selector {
  width: 100% !important;
  height: 38px !important;
  line-height: 38px !important;
  padding: 0 11px !important;
  border-radius: 6px !important;
  display: flex !important;
  align-items: center !important;
  border: 1px solid #d9d9d9 !important;
  box-shadow: none !important;
}

/* 确保下拉菜单样式正确 */
.attendance-form .ant-select-dropdown {
  min-width: 100% !important;
  width: 100% !important;
  border-radius: 6px !important;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12),
              0 6px 16px 0 rgba(0, 0, 0, 0.08),
              0 9px 28px 8px rgba(0, 0, 0, 0.05) !important;
}

/* 特别针对课程选择下拉框 */
.course-select .ant-select-selector {
  width: 100% !important;
}

/* 第一行：课程、日期、类型 - 精确对齐布局 */
.course-date-row {
  display: grid !important;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 16px !important;
  align-items: start !important;
  margin-bottom: 0 !important;
}

.course-date-row .course-item,
.course-date-row .date-item,
.course-date-row .type-item {
  min-width: 0 !important;
}

/* 第一行字段样式继承全局设置 */

/* 第二行：开始时间、结束时间、课时 - 精确对齐布局 */
.time-duration-row {
  display: grid !important;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 16px !important;
  align-items: start !important;
  margin-bottom: 0 !important;
}

.time-duration-row .time-item,
.time-duration-row .duration-item {
  min-width: 0 !important;
}

/* 第二行字段样式继承全局设置 */

/* 课时字段特殊布局对齐 */
.time-duration-row .duration-item .duration-input-container {
  display: flex;
  height: 38px;
  border-radius: 8px;
  overflow: hidden;
}

.time-duration-row .duration-item .duration-input-container .ant-input {
  height: 38px !important;
  border-radius: 0 !important;
}

/* 课程和日期行中的课程选择框 */
.course-date-row .course-item .ant-select-selector {
  width: 100% !important;
  border-radius: 6px !important;
}

.course-select + .ant-select-dropdown,
.course-select ~ .ant-select-dropdown,
.attendance-form .course-select-dropdown {
  width: 100% !important;
  min-width: 100% !important;
  max-width: 100% !important;
  left: 0 !important;
}

/* 强制下拉菜单宽度与选择框一致 */
.ant-select-dropdown-placement-bottomLeft,
.ant-select-dropdown-placement-topLeft {
  left: 0 !important;
}

/* 确保占位符文本可见 */
.attendance-form .ant-select-selection-placeholder {
  opacity: 1 !important;
  color: rgba(0, 0, 0, 0.25) !important;
  display: block !important;
  visibility: visible !important;
}

/* 确保下拉选项文本不换行 */

/* 类型选择框样式 */
.course-date-row .type-item .ant-select {
  width: 100% !important;
}

.course-date-row .type-item .ant-select-selector {
  border-radius: 8px !important;
  height: 38px !important;
  border: 1px solid #d9d9d9 !important;
  display: flex !important;
  align-items: center !important;
  padding: 0 11px !important;
}

.course-date-row .type-item .ant-select-selection-item {
  line-height: 38px !important;
  padding: 0 !important;
}

.course-date-row .type-item .ant-select-selection-placeholder {
  line-height: 38px !important;
  color: rgba(0, 0, 0, 0.25) !important;
}

/* 类型选择框下拉菜单样式 */
.course-date-row .type-item .ant-select-dropdown {
  border-radius: 8px !important;
  width: 100% !important;
  min-width: 100% !important;
}

/* 强制下拉菜单与选择框对齐 */
.attendance-modal .ant-select-dropdown {
  border-radius: 8px !important;
}

.attendance-modal .ant-select-dropdown .ant-select-item {
  padding: 8px 12px !important;
}

.attendance-modal .ant-select-dropdown .ant-select-item-option-content {
  white-space: nowrap !important;
}

/* 专门针对类型选择框的下拉菜单 */
.type-select-dropdown {
  left: 0 !important;
  width: 100% !important;
  min-width: 100% !important;
  border-radius: 8px !important;
}

.type-select-dropdown .ant-select-item {
  padding: 8px 12px !important;
  text-align: left !important;
}

.type-select-dropdown .ant-select-item-option-content {
  white-space: nowrap !important;
  display: block !important;
}

/* 类型标签信息图标样式 */
.type-item .ant-form-item-label label {
  display: flex !important;
  align-items: center !important;
  gap: 4px !important;
}

.type-item .ant-form-item-label .anticon-info-circle {
  transition: color 0.3s ease !important;
}

.type-item .ant-form-item-label .anticon-info-circle:hover {
  color: #40a9ff !important;
}
.attendance-form .ant-select-item-option-content {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* 修复时间选择器样式 */
.attendance-form .ant-picker {
  width: 100% !important;
  border-radius: 6px !important;
}

/* 课程和日期行中的日期选择器 */
.course-date-row .date-item .ant-picker {
  width: 100% !important;
  border-radius: 6px !important;
}

/* 确保禁用状态的输入框样式正确 */
.attendance-form .ant-input[disabled] {
  background-color: #f5f5f5 !important;
  color: rgba(0, 0, 0, 0.25) !important;
  cursor: not-allowed !important;
}

/* 确保表单项标签样式正确 */
.attendance-form .ant-form-item-label > label {
  font-weight: normal;
  color: rgba(0, 0, 0, 0.85);
}

/* 确保时间和课时行中的表单项标签对齐 */
.time-duration-row .ant-form-item-label,
.course-date-row .ant-form-item-label {
  min-height: 22px;
  height: 22px;
  line-height: 22px;
}

/* 确保必填项标记样式正确 */
.attendance-form .ant-form-item-required::before {
  color: #ff4d4f !important;
}

/* 全局覆盖样式，确保下拉菜单宽度与选择框一致 */
.ant-select-dropdown {
  min-width: 100% !important;
  width: 100% !important;
}

/* 确保下拉菜单位置正确 */
.ant-select-dropdown-placement-bottomLeft,
.ant-select-dropdown-placement-topLeft {
  left: 0 !important;
}

/* 确保下拉菜单内容不会被截断 */
.ant-select-item {
  padding: 5px 12px !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* 课时输入框容器样式 */
.duration-input-container {
  display: flex;
  align-items: stretch;
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
}

/* 时间和课时行中的课时输入框容器样式 */
.time-duration-row .duration-input-container {
  display: flex;
  align-items: stretch;
  width: 100%;
  margin-top: 0;
  border-radius: 8px;
  overflow: hidden;
}

/* 课时单位样式 */
.duration-unit {
  margin-left: 0;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 38px;
  white-space: nowrap;
}

/* 时间和课时行中的课时单位样式 */
.time-duration-row .duration-unit {
  margin-left: 0;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 38px;
  white-space: nowrap;
  text-align: left;
}

/* 通用行样式 */
.course-date-row,
.time-duration-row {
  margin: 0;
}

/* 通用项样式 */
.course-date-row .course-item,
.course-date-row .date-item,
.course-date-row .type-item,
.time-duration-row .time-item,
.time-duration-row .duration-item {
  flex: 1;
  padding: 0 8px;
  margin-bottom: 20px;
}

/* 课时项中的输入框样式 */
.time-duration-row .duration-item .ant-input {
  text-align: center;
  padding: 0;
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
  flex: 1;
  width: 50% !important; /* 平分宽度 */
}

/* 确保课时输入框和单位在一行中居中显示 */
.time-duration-row .duration-item .duration-input-container {
  display: flex;
  justify-content: center;
  align-items: stretch;
  overflow: hidden;
}

/* 课时单位在时间和课时行中的样式 */
.time-duration-row .duration-item .duration-unit {
  width: 50% !important; /* 平分宽度 */
  flex: 1;
}

/* 自定义时间选择器样式，确保小时和分钟列宽度一致 */
.time-picker-popup .ant-picker-footer {
  display: none !important;
}

/* 强制设置面板容器宽度 */
.time-picker-popup .ant-picker-panel-container,
.time-picker-popup .ant-picker-panel,
.time-picker-popup .ant-picker-time-panel {
  width: 140px !important;
  min-width: 140px !important;
  max-width: 140px !important;
}

/* 保证面板中的两列布局 */
.time-picker-popup .ant-picker-time-panel {
  display: flex !important;
  flex-direction: row !important;
  justify-content: center !important;
}

/* 强制每个列等宽 */
.time-picker-popup .ant-picker-time-panel-column {
  width: 70px !important;
  min-width: 70px !important;
  max-width: 70px !important;
  flex: 0 0 70px !important;
  text-align: center !important;
  padding: 0 !important;
  margin: 0 !important;
  overflow-x: hidden !important;
}

/* 子元素保持在容器中居中 */
.time-picker-popup .ant-picker-time-panel-column > li,
.time-picker-popup .ant-picker-time-panel-cell {
  width: 100% !important;
  text-align: center !important;
  margin: 0 auto !important;
  padding: 0 !important;
  height: 28px !important;
  line-height: 28px !important;
}

/* 改进所有时间单元格样式 */
.time-picker-popup .ant-picker-time-panel-cell-inner {
  font-size: 14px !important;
  border-radius: 4px !important;
  margin: 1px 2px !important;
  width: calc(100% - 4px) !important;
  transition: all 0.1s !important;
  height: 26px !important;
  line-height: 26px !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: #333333 !important;
}

/* 选中项样式 */
.time-picker-popup .ant-picker-time-panel-cell-selected .ant-picker-time-panel-cell-inner {
  background-color: #1890ff !important;
  color: white !important;
  font-weight: 500 !important;
  border-radius: 4px !important;
  box-shadow: none !important;
  transition: background-color 0.2s !important;
}

/* 鼠标悬停样式 */
.time-picker-popup .ant-picker-time-panel-cell-inner:hover {
  background-color: #e6f7ff !important;
  color: #1890ff !important;
  font-weight: 500 !important;
  box-shadow: none !important;
}

/* 确保选中项的悬停效果不变 */
.time-picker-popup .ant-picker-time-panel-cell-selected .ant-picker-time-panel-cell-inner:hover {
  background-color: #1890ff !important;
  color: white !important;
}

/* 确保列高度足够显示所有内容 */
.time-picker-popup .ant-picker-time-panel-column {
  height: 250px !important;
}

/* 移除可能影响布局的边框 */
.time-picker-popup .ant-picker-time-panel-column {
  border: none !important;
}

/* 自定义滚动条样式，确保不会占用太多空间 */
.time-picker-popup .ant-picker-time-panel-column::-webkit-scrollbar {
  width: 4px !important;
}

.time-picker-popup .ant-picker-time-panel-column::-webkit-scrollbar-track {
  background: #f0f0f0 !important;
}

.time-picker-popup .ant-picker-time-panel-column::-webkit-scrollbar-thumb {
  background: #ccc !important;
  border-radius: 2px !important;
}

/* 强制点击样式，点击选项后自动确认 */
.time-picker-popup .ant-picker-time-panel-column .ant-picker-time-panel-cell {
  cursor: pointer !important;
}

.time-picker-popup .ant-picker-time-panel-column .ant-picker-time-panel-cell:active::after {
  content: "";
  display: block;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  pointer-events: none;
}

/* 隐藏确认按钮 */
.time-picker-popup .ant-picker-ok {
  display: none !important;
}

/* 时间选择器样式优化 */
.ant-time-picker-panel-select-option-selected {
  background-color: #40a9ff !important;
  color: white !important;
  font-weight: 500 !important;
  border-radius: 4px !important;
}

.ant-picker-time-panel-column > li {
  font-size: 14px !important;
  border-radius: 4px !important;
  margin: 2px 4px !important;
  width: calc(100% - 8px) !important;
  height: 28px !important;
  line-height: 28px !important;
  padding: 0 !important;
  text-align: center !important;
  transition: all 0.2s !important;
}

.ant-picker-time-panel-column > li.ant-picker-time-panel-cell-selected .ant-picker-time-panel-cell-inner {
  background-color: #40a9ff !important;
  color: white !important;
  border-radius: 4px !important;
  font-weight: 500 !important;
  transition: all 0.2s !important;
}

.ant-picker-time-panel-column > li .ant-picker-time-panel-cell-inner:hover {
  background-color: #e6f7ff !important;
}

/* 时间选择器样式 */
.ant-picker-dropdown .ant-picker-time-panel-column {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.ant-picker-dropdown .ant-picker-time-panel-column::-webkit-scrollbar {
  display: none;
}

.ant-picker-dropdown .ant-picker-time-panel-column > li.ant-picker-time-panel-cell {
  font-size: 14px;
  border-radius: 4px;
  margin: 2px 0;
  width: 100%;
  height: 34px;
  line-height: 34px;
  padding: 0 16px;
  transition: all 0.2s;
}

.ant-picker-dropdown .ant-picker-time-panel-column > li.ant-picker-time-panel-cell:hover {
  background-color: #e6f7ff;
}

.ant-picker-dropdown .ant-picker-time-panel-column > li.ant-picker-time-panel-cell .ant-picker-time-panel-cell-inner {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.ant-picker-dropdown .ant-picker-time-panel-column > li.ant-picker-time-panel-cell-selected .ant-picker-time-panel-cell-inner {
  background-color: #40a9ff;
  color: white;
  font-weight: 500;
  transition: all 0.3s;
}

/* 隐藏滚动条 */
.time-picker-popup .ant-picker-time-panel-column {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.time-picker-popup .ant-picker-time-panel-column::-webkit-scrollbar {
  display: none;
}

/* 时间选择器选项样式 */
.time-picker-popup .ant-picker-time-panel-column > li {
  font-size: 14px;
  border-radius: 4px;
  margin: 4px 0;
  width: 80%;
  height: 32px;
  line-height: 32px;
  padding: 0 8px;
  transition: all 0.2s;
}

/* 选中的时间选项样式 */
.time-picker-popup .ant-picker-time-panel-column > li.ant-picker-time-panel-cell-selected .ant-picker-time-panel-cell-inner {
  background-color: #40a9ff;
  color: white;
  font-weight: 500;
  transition: all 0.2s;
  border: none;
}

/* 鼠标悬停时间选项样式 */
.time-picker-popup .ant-picker-time-panel-column > li:hover {
  background-color: #e6f7ff;
}

/* 全局表单字段对齐优化 - 完全重写对齐规则 */
.attendance-form {
  width: 100%;
}

/* 强制重置所有Form.Item的样式 */
.attendance-form .ant-form-item {
  margin-bottom: 24px !important;
  margin-top: 0 !important;
  position: relative !important;
}

/* 统一所有标签样式 */
.attendance-form .ant-form-item-label {
  text-align: left !important;
  padding: 0 !important;
  height: 22px !important;
  line-height: 22px !important;
  margin-bottom: 8px !important;
  display: block !important;
  position: relative !important;
}

.attendance-form .ant-form-item-label > label {
  height: 22px !important;
  line-height: 22px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  color: #333 !important;
  margin: 0 !important;
  padding: 0 !important;
  display: inline-block !important;
  vertical-align: top !important;
}

/* 统一所有输入控件容器 */
.attendance-form .ant-form-item-control {
  height: 38px !important;
  line-height: 38px !important;
  position: relative !important;
}

.attendance-form .ant-form-item-control-input {
  height: 38px !important;
  line-height: 38px !important;
}

.attendance-form .ant-form-item-control-input-content {
  height: 38px !important;
  line-height: 38px !important;
}

/* 强制统一所有输入组件的高度和样式 */
.attendance-form .ant-input,
.attendance-form .ant-picker,
.attendance-form .ant-select,
.attendance-form .ant-select-selector,
.attendance-form .ant-input-number,
.attendance-form .duration-input-container {
  height: 38px !important;
  line-height: 36px !important;
  border-radius: 8px !important;
  border: 1px solid #d9d9d9 !important;
  box-sizing: border-box !important;
  display: flex !important;
  align-items: center !important;
}

/* 确保所有标签文字都正确显示 */
.attendance-form .ant-form-item-label > label {
  display: flex !important;
  align-items: center !important;
  color: rgba(0, 0, 0, 0.85) !important;
  font-size: 14px !important;
  white-space: nowrap !important;
  overflow: visible !important;
}

/* 特别确保类型字段标签显示 */
.course-date-row .type-item .ant-form-item-label,
.course-date-row .type-item .ant-form-item-label > label {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
  color: rgba(0, 0, 0, 0.85) !important;
}

/* 修复备注字段的样式 */
.attendance-form .ant-input {
  padding: 11px !important;
  line-height: 1.5715 !important;
}

.attendance-form .ant-input::placeholder {
  color: rgba(0, 0, 0, 0.25) !important;
  line-height: 1.5715 !important;
}

/* 备注文本域样式优化 */
.attendance-form .ant-input.ant-input {
  height: auto !important;
  min-height: 32px !important;
  padding: 11px !important;
  line-height: 1.5715 !important;
  resize: none !important;
}

.attendance-form textarea.ant-input {
  height: auto !important;
  min-height: 88px !important;
  padding: 11px !important;
  line-height: 1.5715 !important;
  resize: none !important;
  vertical-align: top !important;
}

.attendance-form textarea.ant-input::placeholder {
  color: rgba(0, 0, 0, 0.25) !important;
  line-height: 1.5715 !important;
  vertical-align: top !important;
}

/* 统一Select内文本与占位符的行高，保证垂直居中 */
.attendance-form .ant-select-single .ant-select-selector .ant-select-selection-item,
.attendance-form .ant-select-single .ant-select-selector .ant-select-selection-placeholder {
  line-height: 38px !important;
}

/* 统一Input高度 */
.attendance-form .ant-input {
  height: 38px !important;
  line-height: 38px !important;
}

/* 统一DatePicker/TimePicker高度 */
.attendance-form .ant-picker {
  height: 38px !important;
}
.attendance-form .ant-picker .ant-picker-input > input {
  height: 38px !important;
  line-height: 38px !important;
}
