.student-analysis-container {
  padding: 12px;
}

.student-analysis-layout {
  display: flex;
  gap: 24px;
}

.student-analysis-main-column {
  flex: 3;
  display: flex;
  flex-direction: column;
  gap: 24px;
  min-width: 0;
}

.student-analysis-side-column {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 24px;
  min-width: 0;
}

.chart-container {
  position: relative;
  width: 100%;
}

.chart-loading-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 10;
} 