.statistic-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #f0f0f0;
  height: 100%;
  transition: all 0.3s ease;
}

.statistic-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
  transform: translateY(-4px);
}

.statistic-card .ant-card-body {
  padding: 20px;
}

.statistic-card-inner {
  display: flex;
  align-items: center;
}

.statistic-card-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  flex-shrink: 0;
}

.statistic-card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding-right: 12px;
}

.statistic-card-title {
  color: #8c8c8c;
  font-size: 14px;
  margin-bottom: 12px;
  display: block;
  text-align: center;
}

.statistic-card-value-row {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  min-height: 32px; /* 确保加载状态和数据显示时高度一致 */
}

.statistic-card-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 32px;
}

.statistic-card-value {
  display: flex;
  align-items: baseline;
  gap: 2px;
}

.statistic-card-prefix {
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.statistic-card-number {
  color: #333;
  font-size: 22px;
  font-weight: 600;
}

.statistic-card-growth {
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  white-space: nowrap;
}