.statistics-overview-container {
  padding: 12px;
}

.statistics-overview-layout {
  display: flex;
  gap: 24px;
}

.statistics-cards-column {
  flex: 3;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.statistics-chart-column {
  flex: 4;
  min-width: 0; /* Prevents chart from overflowing */
}

.statistics-group {
  background-color: #fff;
  padding: 24px;
  border-radius: 12px;
  border: 1px solid #f0f0f0;
}

.statistics-group-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
  text-align: center;
}

.chart-container-card {
  background-color: #fff;
  padding: 24px;
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  height: 100%;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.chart-title {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  text-align: center;
}

.chart-wrapper {
  position: relative;
} 