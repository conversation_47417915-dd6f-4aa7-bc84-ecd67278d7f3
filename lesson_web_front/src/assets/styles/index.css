/* Import global dropdown styles */
@import url('./globalDropdown.css');

:root {
  --primary: #3498db;
  --secondary: #2ecc71;
  --dark: #34495e;
  --light: #ecf0f1;
  --danger: #e74c3c;
  --warning: #f39c12;
  --info: #3498db;
  --border: #e9ecef;
  --bg-light: #f5f7fa;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

body {
  background-color: #f5f7fa;
  color: #333;
  min-height: 100vh;
}

/* 通用容器 */
.container {
  display: flex;
  min-height: 100vh;
  background-color: #f8fafc;
  position: relative;
}

/* 侧边栏样式 */
.sidebar {
  width: 240px;
  background: linear-gradient(135deg, #5c6e91 0%, #374263 100%);
  color: white;
  padding: 20px 0;
  transition: all 0.3s ease;
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  z-index: 100;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
}

.sidebar.collapsed {
  width: 70px;
}

.sidebar-header {
  padding: 0 20px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 15px;
  position: relative;
}

.sidebar-logo {
  text-align: center;
  margin-bottom: 5px;
}

.sidebar-logo img {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  object-fit: contain;
}

.sidebar.collapsed .sidebar-logo img {
  width: 40px;
  height: 40px;
}

.sidebar-toggle {
  position: absolute;
  top: -15px;
  right: 10px;
  background: rgba(255, 255, 255, 0.15);
  border: none;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  z-index: 101;
}

.sidebar-toggle:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.sidebar-menu {
  list-style: none;
  padding: 0 15px; /* 减少侧边栏菜单的左右内边距 */
}

.sidebar-menu li {
  margin-bottom: 2px;
}

.sidebar-menu a {
  display: flex;
  align-items: center;
  padding: 10px 12px; /* 减少左右内边距 */
  color: rgba(255, 255, 255, 0.85);
  text-decoration: none;
  border-radius: 8px;
  transition: all 0.3s;
  font-weight: 500;
  justify-content: flex-start;
  text-align: center;
}

.sidebar.collapsed .sidebar-menu a {
  justify-content: center;
}

.sidebar-menu a i,
.sidebar-menu a svg {
  margin-right: 8px; /* 减少图标与文字的间距 */
  width: 24px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  transition: all 0.3s;
  flex-shrink: 0;
  position: relative;
  top: 1px; /* 微调图标位置使其与文字垂直对齐 */
}

.sidebar.collapsed .sidebar-menu a i,
.sidebar.collapsed .sidebar-menu a svg {
  margin-right: 0;
  margin-left: 0;
  font-size: 20px;
}

.sidebar-menu a span {
  padding-left: 0; /* 移除文字的左内边距 */
  flex: 1;
  text-align: left;
  letter-spacing: 0.5px; /* 增加字间距使文字更清晰 */
}

.sidebar-menu a:hover,
.sidebar-menu a.active {
  background-color: rgba(255, 255, 255, 0.15);
  color: white;
  transform: translateX(3px);
}

.sidebar.collapsed .sidebar-menu a:hover,
.sidebar.collapsed .sidebar-menu a.active {
  transform: translateX(0);
}

.sidebar-menu a.active {
  background-color: rgba(255, 255, 255, 0.25);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

/* 主内容区样式 */
.main-content {
  flex: 1;
  padding: 20px;
  padding-left: 260px;
  overflow-y: auto;
  transition: all 0.3s ease;
  position: relative;
}

.sidebar.collapsed + .main-content {
  padding-left: 90px;
}

/* 卡片和表格通用样式 */
.card, .dashboard-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  overflow: hidden;
  width: 100%;
  transition: opacity 0.2s;
  cursor: default;
}

.dashboard-card.dragging, .card.dragging {
  opacity: 1;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
  background-color: #fff;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--dark);
  display: flex;
  align-items: center;
}

.card-body {
  padding: 20px;
}

/* 表格样式 */
.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 12px 15px;
  border-bottom: 1px solid #eee;
  text-align: left;
}

.table th {
  font-weight: 600;
  color: var(--dark);
  background-color: #f8f9fa;
  position: sticky;
  top: 0;
  z-index: 10;
}

.table tr:last-child td {
  border-bottom: none;
}

.table tbody tr:hover {
  background-color: #f8f9fa;
}

.content-panel {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.05);
  padding: 20px;
  margin-bottom: 20px;
  transition: all 0.3s ease;
  position: relative;
  z-index: 10;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eef2f7;
  transition: all 0.3s ease;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #3a4f66;
  transition: all 0.3s ease;
}

/* 校区信息 */
.campus-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  background: linear-gradient(135deg, #3498db, #2ecc71);
  color: white;
  border-radius: 0;
  margin-bottom: 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: -webkit-sticky; /* For Safari */
  position: sticky;
  top: 0;
  z-index: 990;
  width: 100%;
}

.campus-info-content {
  flex: 1;
}

.campus-name {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 10px;
}

.campus-details {
  display: flex;
  gap: 30px;
}

.campus-detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.campus-detail-item i {
  font-size: 18px;
}

.campus-detail-item span {
  font-size: 14px;
  opacity: 0.9;
}

.campus-status-badge {
  background-color: rgba(255, 255, 255, 0.2);
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

/* 校区选择器样式 */
.campus-selector {
  padding: 0 20px;
  margin-bottom: 20px;
  color: rgba(255, 255, 255, 0.85);
  position: relative;
  z-index: 1001; /* Ensure the selector has a high z-index */
}

.campus-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 15px;
  cursor: pointer;
  user-select: none;
  background-color: rgba(255, 255, 255, 0.08);
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
}

.campus-header:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.campus-header span {
  font-weight: 500;
  color: white;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.campus-header span::before {
  content: '🏢';
  margin-right: 8px;
  font-size: 16px;
}

.campus-header::after {
  content: '▶';
  font-size: 10px;
  margin-left: 8px;
  color: rgba(255, 255, 255, 0.8);
  transition: transform 0.3s ease;
}

.campus-header.active::after {
  transform: rotate(90deg);
}

.campus-header.active {
  background-color: rgba(255, 255, 255, 0.25);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.campus-list {
  display: none;
  list-style: none;
  position: absolute;
  right: 0;
  top: 100%;
  background-color: #2c3e50;
  min-width: 160px;
  border-radius: 0 0 4px 4px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
  z-index: 1000;
  padding: 10px;
  margin-top: 2px;
}

.campus-list.show {
  display: block;
}

.campus-item {
  padding: 8px 10px;
  margin: 5px 0;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.3s;
  white-space: nowrap;
}

.campus-item:hover {
  background-color: rgba(255, 255, 255, 0.15);
  transform: translateX(3px);
}

.campus-item.active {
  background-color: rgba(255, 255, 255, 0.25);
  color: white;
  font-weight: 500;
}

/* 仪表盘统计 */
.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  padding: 10px;
}

.stat-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 20px;
  display: flex;
  flex-direction: column;
  text-align: center;
  height: 100%;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* 特殊宽度的统计卡片 */
.stat-card.wide {
  grid-column: span 2;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  margin: 10px 0;
  color: var(--primary);
}

.stat-title {
  color: #777;
  font-size: 14px;
  margin-bottom: 5px;
}

/* 统计数据条样式 */
.stats-bar {
  display: flex;
  background: linear-gradient(120deg, #f8f9fa, #ffffff, #f8f9fa);
  background-size: 200% 100%;
  border-radius: 10px;
  padding: 12px 5px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.04);
  margin-bottom: 15px;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #f1f1f1;
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 0 10px;
  position: relative;
  min-width: 120px;
  flex: 1;
  transition: transform 0.2s, background-color 0.2s;
}

.stat-item:hover {
  background-color: rgba(52, 152, 219, 0.05);
  transform: translateY(-2px);
}

.stat-content {
  text-align: center;
  width: 100%;
}

.stat-icon {
  margin-bottom: 3px;
  font-size: 16px;
  color: #3498db;
  opacity: 0.8;
  background-color: rgba(52, 152, 219, 0.1);
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin: 0 auto 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s, background-color 0.2s;
}

.stat-item:hover .stat-icon {
  transform: scale(1.1);
  background-color: rgba(52, 152, 219, 0.2);
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #3498db;
  margin-bottom: 3px;
}

/* Individual stat item colors */
.stat-item:nth-child(1) .stat-number {
  color: #3498db; /* Blue */
}

.stat-item:nth-child(2) .stat-number {
  color: #2ecc71; /* Green */
}

.stat-item:nth-child(3) .stat-number {
  color: #e74c3c; /* Red */
}

.stat-item:nth-child(4) .stat-number {
  color: #f39c12; /* Orange */
}

.stat-item:nth-child(5) .stat-number {
  color: #9b59b6; /* Purple */
}

.stat-item:nth-child(6) .stat-number {
  color: #e67e22; /* Dark Orange */
}

.stat-item:nth-child(7) .stat-number {
  color: #16a085; /* Teal */
}

.stat-item:nth-child(8) .stat-number {
  color: #27ae60; /* Dark Green */
}

/* Item background hover colors */
.stat-item:nth-child(1):hover {
  background-color: rgba(52, 152, 219, 0.08);
}

.stat-item:nth-child(2):hover {
  background-color: rgba(46, 204, 113, 0.08);
}

.stat-item:nth-child(3):hover {
  background-color: rgba(231, 76, 60, 0.08);
}

.stat-item:nth-child(4):hover {
  background-color: rgba(243, 156, 18, 0.08);
}

.stat-item:nth-child(5):hover {
  background-color: rgba(155, 89, 182, 0.08);
}

.stat-item:nth-child(6):hover {
  background-color: rgba(230, 126, 34, 0.08);
}

.stat-item:nth-child(7):hover {
  background-color: rgba(22, 160, 133, 0.08);
}

.stat-item:nth-child(8):hover {
  background-color: rgba(39, 174, 96, 0.08);
}

.stat-item:hover .stat-number {
  color: inherit;
}

.stat-unit {
  font-size: 14px;
  color: #777;
  margin-left: 2px;
}

.stat-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.stat-divider {
  position: absolute;
  right: 0;
  top: 20%;
  bottom: 20%;
  width: 1px;
  background-color: rgba(0,0,0,0.06);
}

/* 搜索和筛选区域 */
.search-filter-container {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.search-box {
  flex: 1;
  min-width: 200px;
}

.search-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.filter-group {
  display: flex;
  gap: 10px;
  align-items: center;
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  min-width: 120px;
}

/* 按钮样式 */
.action-btn {
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  margin: 0 3px;
}

.action-btn-primary {
  background-color: var(--primary);
  color: white;
}

.action-btn-secondary {
  background-color: var(--secondary);
  color: white;
}

.action-btn-warning {
  background-color: var(--warning);
  color: white;
}

.action-btn-danger {
  background-color: var(--danger);
  color: white;
}

/* 登录/注册容器样式 */
.login-page {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
}

.login-container, .register-container {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 5px 30px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 420px;
  padding: 40px;
  text-align: center;
}

/* Logo样式 */
.logo {
  margin-bottom: 30px;
}

.logo img {
  width: 120px;
  height: auto;
}

/* 标题样式 */
.login-header, .register-header {
  margin-bottom: 30px;
}

.login-header h1, .register-header h1 {
  font-size: 24px;
  font-weight: 600;
  color: var(--dark);
  margin-bottom: 10px;
}

.login-header p, .register-header p {
  font-size: 15px;
  color: var(--secondary);
}

/* 表单样式 */
.login-form, .register-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  text-align: left;
}

.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: var(--dark);
  margin-bottom: 8px;
}

.form-group input {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid var(--border);
  border-radius: 5px;
  font-size: 14px;
  color: var(--dark);
  outline: none;
  transition: border-color 0.3s, box-shadow 0.3s;
}

.form-group input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(74, 108, 247, 0.15);
}

/* 按钮样式 */
.btn {
  width: 100%;
  padding: 12px;
  border-radius: 5px;
  background-color: var(--primary);
  color: white;
  border: none;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s;
  margin-top: 10px;
}

.btn:hover {
  background-color: #2980b9;
  box-shadow: 0 2px 10px rgba(52, 152, 219, 0.4);
}

.btn-sm {
  padding: 5px 10px;
  font-size: 14px;
  width: auto;
  margin-top: 0;
}

.btn-secondary {
  background-color: var(--secondary);
  color: white;
}

.btn-secondary:hover {
  background-color: #27ae60;
  box-shadow: 0 2px 10px rgba(46, 204, 113, 0.4);
}

/* 首次登录提示 */
.first-login-note {
  margin-top: 25px;
  padding: 12px;
  background-color: #fff8e1;
  border-left: 4px solid #ffc107;
  border-radius: 4px;
  text-align: left;
  font-size: 13px;
  color: #856404;
}

/* 用户信息和下拉菜单样式 */
.user-info {
  display: flex;
  align-items: center;
  position: relative;
  gap: 10px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 50px;
  transition: all 0.3s ease;
}

.user-info:hover {
  background-color: #f8fafc;
}

.campus-info .user-info:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #5c6e91 0%, #374263 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  transition: all 0.3s ease;
}

/* 修改校区信息栏用户头像 */
.campus-info .user-avatar {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.5);
}

.dropdown-content {
  display: none;
  position: absolute;
  right: 0;
  top: 100%;
  background-color: white;
  min-width: 150px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  z-index: 1000;
  overflow: hidden;
  opacity: 0;
  transform: translateY(10px);
  transition: opacity 0.3s, transform 0.3s;
}

.dropdown-content.show {
  display: block;
  opacity: 1;
  transform: translateY(0);
}

.dropdown-content a {
  color: #3a4f66;
  padding: 10px 15px;
  text-decoration: none;
  display: block;
  font-size: 14px;
  transition: all 0.3s;
  border-left: 3px solid transparent;
}

.dropdown-content a:hover {
  background-color: #f8fafc;
  color: #2575fc;
  border-left: 3px solid #2575fc;
}

/* 登录/注册链接 */
.login-link, .register-link {
  margin-top: 20px;
  text-align: center;
  font-size: 14px;
}

.login-link a, .register-link a {
  color: var(--primary);
  text-decoration: none;
  font-weight: 600;
  margin-left: 5px;
}

.login-link a:hover, .register-link a:hover {
  text-decoration: underline;
}

/* 脉冲动画效果 - 已移除 */

/* Loading spinner */
@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-spinner {
  border-radius: 50%;
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0,0,0,0.1);
  border-top-color: var(--primary);
  animation: spin 1s linear infinite;
  margin-right: 10px;
}

/* 教练卡片样式 */
.coach-card {
  flex: 1 1 30%;
  min-width: 300px;
  max-width: none;
  background-color: white;
  color: #333;
  padding: 18px;
  border-radius: 8px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
  margin-bottom: 10px;
  transition: all 0.3s ease;
}

.coach-card:nth-child(1) {
  border-top: 3px solid #3498db;
}

.coach-card:nth-child(2) {
  border-top: 3px solid #2ecc71;
}

.coach-card:nth-child(3) {
  border-top: 3px solid #f39c12;
}

.coach-card .student-list {
  font-size: 13px;
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
  min-height: 140px;
  max-height: 180px;
  overflow-y: auto;
  box-shadow: inset 0 0 5px rgba(0,0,0,0.02);
}

.coach-card .student-list > div {
  column-count: 1;
  column-gap: 15px;
}

/* Apply two-column layout for coach cards with more than 5 student items */
.coach-card:nth-child(1) .student-list > div,
.coach-card:nth-child(3) .student-list > div {
  column-count: 2;
}

.coach-card .student-list .student-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  break-inside: avoid;
  padding: 5px 8px;
  border-radius: 4px;
  background-color: rgba(52, 152, 219, 0.03);
}

.coach-card:nth-child(1) .student-list .student-item {
  background-color: rgba(52, 152, 219, 0.03);
}

.coach-card:nth-child(1) .student-list .student-item:nth-child(even) {
  background-color: rgba(255, 255, 255, 0.4);
}

.coach-card:nth-child(2) .student-list .student-item {
  background-color: rgba(46, 204, 113, 0.03);
}

.coach-card:nth-child(2) .student-list .student-item:nth-child(even) {
  background-color: rgba(255, 255, 255, 0.4);
}

.coach-card:nth-child(3) .student-list .student-item {
  background-color: rgba(243, 156, 18, 0.03);
}

.coach-card:nth-child(3) .student-list .student-item:nth-child(even) {
  background-color: rgba(255, 255, 255, 0.4);
}

/* 今日概览卡片样式 */
#today-overview-card .stats-bar {
  /* 移除动画效果 */
}

/* 教练员课时统计表格样式 */
.coach-lessons-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.coach-lessons-table th {
  padding: 15px;
  font-weight: 600;
  color: #555;
  background-color: #f8f9fa;
  border-bottom: 1px solid #f0f0f0;
  text-align: center;
}

.coach-lessons-table th:first-child {
  text-align: center;
}

.coach-lessons-table th:last-child {
  text-align: center;
}

.coach-lessons-table tbody tr {
  transition: background-color 0.2s;
}

.coach-lessons-table tbody tr:hover {
  background-color: #f8f9fa;
}

.coach-lessons-table tbody tr:nth-child(1) {
  border-left: 4px solid #e74c3c;
}

.coach-lessons-table tbody tr:nth-child(2) {
  border-left: 4px solid #3498db;
}

.coach-lessons-table tbody tr:nth-child(3) {
  border-left: 4px solid #2ecc71;
}

.coach-lessons-table tbody tr:nth-child(4) {
  border-left: 4px solid #f39c12;
}

.coach-lessons-table td {
  padding: 15px;
  border-bottom: 1px solid #f5f5f5;
  text-align: center;
}

.coach-lessons-table td:first-child {
  text-align: center;
}

.coach-lessons-table td:last-child {
  text-align: center;
  font-weight: 500;
}

.coach-lessons-table tfoot tr {
  background-color: #f8f9fa;
  font-weight: 700;
  border-top: 1px solid #f0f0f0;
}

.coach-lessons-table .amount-note {
  color: #888;
  font-size: 13px;
  font-weight: normal;
}

/* 响应式样式调整 */
@media (max-width: 1200px) {
  .dashboard-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .stats-bar {
    flex-wrap: wrap;
  }

  .stat-item {
    min-width: 50%;
    padding: 10px;
  }

  .stat-divider {
    display: none;
  }

  #class-dimension-view {
    overflow-x: auto;
  }

  #class-dimension-view > div {
    min-width: 930px;
  }
}

@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    z-index: 1000;
  }

  .sidebar.collapsed {
    transform: translateX(0);
    width: 240px;
  }

  .sidebar-toggle {
    position: fixed;
    left: 15px;
    top: 15px;
    z-index: 1001;
    background: linear-gradient(135deg, #5c6e91 0%, #374263 100%);
    opacity: 0.9;
  }

  .main-content {
    padding-left: 20px;
    padding-top: 120px; /* Reduced padding for fixed header on mobile */
  }

  .sidebar.collapsed + .main-content {
    padding-left: 20px;
  }

  /* Campus info mobile adjustments */
  .campus-info {
    flex-direction: column;
    border-radius: 0;
    margin-bottom: 0;
    padding: 10px;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
    z-index: 999 !important;
  }

  .campus-info-content {
    width: 100%;
  }

  .campus-info-content > div:last-child {
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 10px !important;
    height: auto !important;
  }

  .campus-info-content > div:last-child > div {
    flex-direction: column !important;
    width: 100% !important;
    gap: 10px !important;
  }

  .campus-name {
    font-size: 18px !important;
  }

  .campus-selector,
  .campus-info-content > div:last-child > div > div {
    width: 100% !important;
    margin-right: 0 !important;
    margin-bottom: 8px !important;
    height: auto !important;
  }

  .campus-info-content button {
    width: 100% !important;
    height: 34px !important;
    justify-content: space-between !important;
    padding: 6px 16px !important;
    font-size: 15px !important;
  }

  .campus-info .user-info {
    margin-top: 5px;
  }

  .campus-status-badge {
    font-size: 12px;
    padding: 4px 8px;
  }

  /* Adjust campus list for mobile */
  .campus-list {
    left: 0;
    top: 100%;
    width: 100%;
  }

  .campus-header.active::after {
    transform: rotate(180deg);
  }

  /* Adjust content panel for fixed header */
  .content-panel {
    margin-top: 210px !important;
  }

  /* Mobile sidebar overlay */
  .sidebar::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: -1;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
  }

  .sidebar.collapsed::before {
    opacity: 1;
    visibility: visible;
    z-index: 999;
  }

  .sidebar.collapsed .sidebar-menu a i {
    margin-right: 14px;
    margin-left: 0;
  }

  /* Adjust card headers for mobile */
  .card-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .card-header > div:last-child {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }

  .dashboard-stats {
    grid-template-columns: 1fr;
  }

  .stat-card.wide {
    grid-column: span 1;
  }

  .stat-item {
    min-width: 100%;
  }

  .campus-status-badge {
    margin-top: 10px;
  }

  .coach-card {
    min-width: 250px;
  }
}

@media (max-width: 480px) {
  .login-container, .register-container {
    margin: 20px;
    padding: 30px 20px;
    max-width: 90%;
  }

  .header {
    flex-direction: column;
    align-items: flex-start;
  }

  .user-info {
    margin-top: 15px;
  }
}

/* 今日上课学员表格样式 */
.attendance-table {
  width: 100%;
  border-collapse: collapse;
}

.attendance-table th {
  background-color: #f8f9fa;
  color: #555;
  font-weight: 600;
  padding: 12px 15px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
}

.attendance-table td {
  padding: 12px 15px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 14px;
  text-align: center;
}

.attendance-table tr:hover {
  background-color: #f9f9f9;
}

.badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.badge-success {
  background-color: rgba(46, 204, 113, 0.15);
  color: #2ecc71;
}

.badge-warning {
  background-color: rgba(243, 156, 18, 0.15);
  color: #f39c12;
}

.badge-danger {
  background-color: rgba(231, 76, 60, 0.15);
  color: #e74c3c;
}

.badge-info {
  background-color: rgba(52, 152, 219, 0.15);
  color: #3498db;
}

.btn-punch {
  background-color: #f0f9ff;
  color: #3498db;
  border: 1px solid #b3e5fc;
  padding: 4px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 3px;
}

.btn-punch:hover {
  background-color: #e3f2fd;
  border-color: #3498db;
}

.btn-punch i {
  font-size: 14px;
}

.btn-expense {
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  padding: 4px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  transition: background-color 0.2s;
}

.btn-expense:hover {
  background-color: #e9ecef;
}

.btn-leave {
  background-color: #fff5f5;
  color: #e74c3c;
  border: 1px solid #ffcdd2;
  padding: 4px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 3px;
}

.btn-leave:hover {
  background-color: #ffe3e3;
  border-color: #e74c3c;
}

.btn-leave i {
  font-size: 14px;
}

.btn-batch-punch {
  background-color: #f0f9ff;
  color: #3498db;
  border: 1px solid #b3e5fc;
  padding: 6px 12px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-batch-punch:hover {
  background-color: #e3f2fd;
  border-color: #3498db;
}

/* 数据统计样式 */
.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 5px;
}

.stats-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.period-tabs {
  display: flex;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.period-tab {
  border: none;
  padding: 5px 12px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.period-tab.active {
  background-color: #4facfe;
  color: white;
}

.period-tab:not(.active) {
  background-color: white;
  color: #666;
}

.period-tab:hover:not(.active) {
  background-color: #f5f5f5;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

.stat-box {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  text-align: center;
  /* 移除过渡效果 */
  border-bottom: 3px solid transparent;
}

.stat-box:hover {
  /* 移除悬停效果 */
}

.stat-box:nth-child(1) {
  border-bottom-color: #3498db;
}

.stat-box:nth-child(2) {
  border-bottom-color: #2ecc71;
}

.stat-box:nth-child(3) {
  border-bottom-color: #f39c12;
}

.stat-box:nth-child(4) {
  border-bottom-color: #e74c3c;
}

.stat-box:nth-child(5) {
  border-bottom-color: #9b59b6;
}

.stat-box:nth-child(6) {
  border-bottom-color: #1abc9c;
}

.stat-box:nth-child(7) {
  border-bottom-color: #f1c40f;
}

.stat-box:nth-child(8) {
  border-bottom-color: #34495e;
}

.stat-box-title {
  color: #777;
  font-size: 14px;
}

.stat-box-value {
  font-size: 28px;
  font-weight: 700;
  color: #3498db;
  margin: 10px 0;
}

.stat-box:nth-child(1) .stat-box-value {
  color: #3498db;
}

.stat-box:nth-child(2) .stat-box-value {
  color: #2ecc71;
}

.stat-box:nth-child(3) .stat-box-value {
  color: #f39c12;
}

.stat-box:nth-child(4) .stat-box-value {
  color: #e74c3c;
}

.stat-box:nth-child(5) .stat-box-value {
  color: #9b59b6;
}

.stat-box:nth-child(6) .stat-box-value {
  color: #1abc9c;
}

.stat-box:nth-child(7) .stat-box-value {
  color: #f1c40f;
}

.stat-box:nth-child(8) .stat-box-value {
  color: #34495e;
}

.stat-box-subtitle {
  font-size: 13px;
  color: #777;
}

.stat-highlight {
  color: #2ecc71;
}

.stat-highlight-alt {
  color: #f39c12;
}

.stat-highlight-primary {
  color: #3498db;
} /* 确保管理员信息区域垂直居中 */
.campus-info .user-info { display: flex; align-items: center; height: 100% !important; }
.campus-info .campus-name { display: flex; align-items: center; height: 100% !important; }
.campus-info-content > div > div:first-child { display: flex !important; align-items: center !important; min-height: 50px !important; }

/* 下拉菜单宽度控制 */
.short-dropdown .ant-select-dropdown-menu {
  max-width: 160px !important;
}

.ant-select-dropdown {
  min-width: auto !important;
}

.ant-select-dropdown-menu-item {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ant-table-cell {
  white-space: normal !important;
}

.page-title {
  font-size: 20px !important;
  font-weight: 600 !important;
}
