/* Modal.css */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.modal-backdrop.open {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background-color: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  position: relative;
  width: 90%;
  max-width: 450px;
  transform: translateY(-20px);
  transition: transform 0.3s ease;
}

.modal-backdrop.open .modal-content {
  transform: translateY(0);
}

.modal-close-btn {
  position: absolute;
  top: 10px;
  right: 15px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #888;
  transition: color 0.2s;
}

.modal-close-btn:hover {
  color: #333;
}

.modal-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
  text-align: center;
}

.modal-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--home-dark, #2c3e50);
}

.modal-body .form-group {
  margin-bottom: 15px;
}

.modal-body label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #555;
}

.modal-body input[type="text"],
.modal-body input[type="password"],
.modal-body input[type="tel"],
.modal-body textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
  box-sizing: border-box;
  resize: vertical;
}

.modal-body input:focus,
.modal-body textarea:focus {
  outline: none;
  border-color: var(--home-primary, #3498db);
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* 注释掉这部分，防止与.button-group中的按钮冲突 */
/*
.modal-body .submit-btn {
  width: 100%;
  padding: 12px;
  background-color: var(--home-accent, #ff7846);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s, transform 0.1s;
  margin-top: 10px;
}

.modal-body .submit-btn:hover {
  background-color: #ff6a2e;
}

.modal-body .submit-btn:active {
  transform: scale(0.98);
}
*/

/* Optional: Add error message styling */
.error-message {
  color: #e74c3c;
  margin-top: 10px;
  font-size: 14px;
  text-align: center;
}

/* Style for required field asterisk */
.required-asterisk {
  color: #e74c3c;
  margin-left: 2px;
}

/* Button group for a row of buttons */
.button-group {
  display: flex;
  justify-content: space-between;
  gap: 10px;
  margin-top: 20px;
}

/* 让这两个按钮有相同的基础样式 */
.button-group button {
  padding: 12px;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.2s;
  flex: 1;
  height: 44px;
  line-height: 1.2;
  text-align: center;
  box-sizing: border-box;
  width: 100%;
  margin: 0;
}

/* 提交按钮专有样式 */
.button-group .submit-btn {
  background-color: #ff7846;
  color: white;
  border-radius: 3px;
}

.button-group .submit-btn:hover {
  background-color: #ff6a2e;
}

.button-group .submit-btn:disabled {
  background-color: #95a5a6;
  cursor: not-allowed;
}

/* 取消按钮专有样式 */
.button-group .cancel-btn {
  background-color: white;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 3px;
}

.button-group .cancel-btn:hover {
  background-color: #f5f5f5;
}

.button-group .cancel-btn:disabled {
  background-color: #eee;
  color: #999;
  cursor: not-allowed;
}

/* 注释掉这些全局样式，以避免与.button-group内的样式冲突 */
/*
.submit-btn {
  background-color: var(--home-primary, #3498db);
  color: white;
  padding: 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.2s;
  flex: 1;
  font-size: 16px;
}

.submit-btn:hover {
  background-color: #2980b9;
}

.submit-btn:disabled {
  background-color: #95a5a6;
  cursor: not-allowed;
}

.cancel-btn {
  background-color: #e0e0e0;
  color: #333;
  padding: 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.2s;
  flex: 1;
  font-size: 16px;
}

.cancel-btn:hover {
  background-color: #ccc;
}

.cancel-btn:disabled {
  background-color: #eee;
  color: #999;
  cursor: not-allowed;
}
*/

.success-message {
  background-color: #d4edda;
  color: #155724;
  padding: 15px;
  margin: 15px 0;
  border-radius: 4px;
  text-align: center;
  font-size: 16px;
}

.success-message p {
  margin: 5px 0;
}

/* Style for large modal */
.modal-content.large {
  max-width: 550px;
}

/* 成功模态框样式 - 现代设计 */
.success-modal {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 20px;
  text-align: center;
  background-color: #ffffff;
  border-radius: 8px;
}

.success-icon {
  font-size: 32px;
  color: #ffffff;
  margin-bottom: 24px;
  height: 80px;
  width: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #52c41a, #389e0d);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6px 16px rgba(82, 196, 26, 0.3);
  animation: pulse 1.5s ease-in-out;
}

@keyframes pulse {
  0% { transform: scale(0.8); opacity: 0; }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); opacity: 1; }
}

.success-title {
  font-size: 28px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 16px;
  letter-spacing: 0.5px;
}

.success-message {
  font-size: 16px;
  color: #595959;
  margin-bottom: 24px;
  background-color: #f6ffed;
  padding: 12px 20px;
  border-radius: 6px;
  border: 1px solid #b7eb8f;
  width: 100%;
  max-width: 360px;
}

.success-info-container {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 16px 24px;
  margin-bottom: 30px;
  width: 100%;
  max-width: 360px;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.05);
}

.success-info-row {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.success-institution,
.success-phone {
  font-size: 16px;
  font-weight: normal;
  color: #262626;
  display: flex;
  align-items: center;
  text-align: left;
  position: relative;
  margin-bottom: 0;
}

.success-institution .label,
.success-phone .label {
  display: inline-block;
  min-width: 80px;
}

.success-institution .label {
  letter-spacing: 5px; /* 保持机构名称的字间距 */
}

.success-institution .colon,
.success-phone .colon {
  display: inline-block;
  width: 10px;
  text-align: center;
}

.success-institution .value,
.success-phone .value {
  display: inline-block;
  margin-left: 8px;
}

.success-institution::before,
.success-phone::before {
  display: inline-block;
  width: 30px;
  text-align: center;
  margin-right: 5px;
}

.success-institution::before {
  content: '🏢';
}

.success-phone::before {
  content: '📱';
}

.success-phone {
  margin-bottom: 0;
}

.button-group {
  display: flex;
  gap: 16px;
  margin-top: 10px;
}

.login-now-btn {
  background: linear-gradient(135deg, #1890ff, #096dd9);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  font-weight: normal;
  transition: all 0.3s;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.login-now-btn:hover {
  background: linear-gradient(135deg, #40a9ff, #1890ff);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
}

.later-btn {
  background: linear-gradient(135deg, #f5f5f5, #e8e8e8);
  color: #595959;
  border: 1px solid #d9d9d9;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  font-weight: normal;
  transition: all 0.3s;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.later-btn:hover {
  color: #1890ff;
  border-color: #1890ff;
  background: linear-gradient(135deg, #f0f8ff, #e6f7ff);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

/* 修复重复的样式 */

/* 人工验证组件样式 */
.human-verification {
  margin-bottom: 10px;
}

.verification-problem {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 16px;
  font-weight: 500;
}

.verification-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.3s;
}

.verification-input.valid {
  border-color: #52c41a;
  background-color: #f6ffed;
}

.verification-input.invalid {
  border-color: #f5222d;
  background-color: #fff1f0;
}

.refresh-btn:hover {
  color: #096dd9;
  text-decoration: underline;
}